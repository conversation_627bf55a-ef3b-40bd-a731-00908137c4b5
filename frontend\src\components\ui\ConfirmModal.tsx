import React from 'react';
import { ProhibitIcon } from '@phosphor-icons/react';
import { motion, AnimatePresence } from 'framer-motion';

interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  variant?: 'danger' | 'primary' | 'warning';
  title: string;
  message: string;
  confirmText: string;
  cancelText: string;
  titleIcon: React.ReactNode;
  confirmIcon: React.ReactNode;
}

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  variant = 'primary',
  title,
  message,
  confirmText,
  cancelText,
  titleIcon,
  confirmIcon,
}) => {
  const variants = {
    danger: {
      iconColor: 'text-red-400',
      confirmBtn: 'bg-red-400 text-white',
      border: 'border-red-400',
    },
    primary: {
      iconColor: 'text-primary-500',
      confirmBtn: 'bg-primary-500 text-white',
      border: 'border-primary-500',
    },
    warning: {
      iconColor: 'text-yellow-400',
      confirmBtn: 'bg-yellow-400 text-white',
      border: 'border-yellow-400',
    },
  };

  const config = variants[variant];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <motion.div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={onClose}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          />

          <motion.div
            className={`relative mx-auto w-full max-w-[536px] rounded-lg border-2 bg-neutral-900 p-6 ${config.border} shadow-2xl`}
            initial={{
              opacity: 0,
              scale: 0.8,
              y: 20,
            }}
            animate={{
              opacity: 1,
              scale: 1,
              y: 0,
            }}
            exit={{
              opacity: 0,
              scale: 0.8,
              y: 20,
            }}
            transition={{
              type: 'spring',
              stiffness: 300,
              damping: 25,
              duration: 0.3,
            }}
          >
            <motion.div
              className="mb-4 flex justify-center"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{
                delay: 0.1,
                type: 'spring',
                stiffness: 300,
                damping: 20,
              }}
            >
              <div
                className={`flex h-16 w-16 items-center justify-center rounded-full ${config.iconColor}`}
              >
                {titleIcon}
              </div>
            </motion.div>

            <motion.div
              className="mb-6 text-center"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.15, duration: 0.3 }}
            >
              <h3 className="mb-2 text-xl font-semibold text-white">{title}</h3>
              <p className="text-sm leading-relaxed text-gray-300">{message}</p>
            </motion.div>

            <motion.div
              className="flex gap-10 px-10"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              <motion.button
                onClick={onConfirm}
                className={`flex flex-2 cursor-pointer items-center justify-center gap-2 rounded-xl px-4 py-3 font-medium transition-colors ${config.confirmBtn}`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              >
                {confirmIcon}
                {confirmText}
              </motion.button>
              <motion.button
                onClick={onClose}
                className={`flex flex-2 cursor-pointer items-center justify-center gap-2 rounded-xl border border-gray-600 px-4 py-3 font-medium text-white transition-colors hover:bg-gray-800`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              >
                <ProhibitIcon weight="bold" size={20} />
                {cancelText}
              </motion.button>
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
