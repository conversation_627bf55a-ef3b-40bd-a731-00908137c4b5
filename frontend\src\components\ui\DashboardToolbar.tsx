import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowCounterClockwiseIcon,
  PencilIcon,
  SquaresFourIcon,
  CornersOutIcon,
  CornersInIcon,
  ShareNetworkIcon,
  UsersIcon,
  TrashIcon,
  PushPinIcon,
  PushPinSlashIcon,
  TrashSimpleIcon,
} from '@phosphor-icons/react';
import { cn } from '@/utils/utlis';
import { ConfirmModal } from '@/components/ui/ConfirmModal';
import { deleteDashboard } from '@/services/dashboardService';

interface DashboardToolbarProps {
  onUpdateReports?: () => void;
  onEditDashboard?: () => void;
  onEditLayout?: () => void;
  onViewMode?: () => void;
  onShare?: () => void;
  onUserAccess?: () => void;
  onDelete?: () => void;
  isEditMode?: boolean;
  className?: string;
  dashboardId?: string | number;
  onDeleted?: () => void;
  isFullscreen?: boolean;
  onFullscreenChange?: (isFullscreen: boolean) => void;
  isUpdatingLayout?: boolean;
}

const DashboardToolbar: React.FC<DashboardToolbarProps> = ({
  onUpdateReports,
  onEditDashboard,
  onEditLayout,
  onViewMode,
  onShare,
  onUserAccess,
  onDelete,
  isEditMode = false,
  className,
  dashboardId,
  onDeleted,
  isFullscreen = false,
  onFullscreenChange,
  isUpdatingLayout = false,
}) => {
  const navigate = useNavigate();
  const [isPinned, setIsPinned] = useState(true);
  const [isVisible, setIsVisible] = useState(true);
  const [hideTimeout, setHideTimeout] = useState<NodeJS.Timeout | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const savedPinnedState = localStorage.getItem('dashboardToolbarPinned');
    if (savedPinnedState !== null) {
      const pinned = JSON.parse(savedPinnedState);
      setIsPinned(pinned);
      setIsVisible(pinned);
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('dashboardToolbarPinned', JSON.stringify(isPinned));
  }, [isPinned]);

  useEffect(() => {
    return () => {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
      }
    };
  }, [hideTimeout]);

  const handlePinToggle = () => {
    const newPinnedState = !isPinned;
    setIsPinned(newPinnedState);

    if (newPinnedState) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }

    if (hideTimeout) {
      clearTimeout(hideTimeout);
      setHideTimeout(null);
    }
  };

  const handleMouseEnterHoverArea = () => {
    if (!isPinned) {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
        setHideTimeout(null);
      }
      setIsVisible(true);
    }
  };

  const handleMouseEnterToolbar = () => {
    if (!isPinned) {
      if (hideTimeout) {
        clearTimeout(hideTimeout);
        setHideTimeout(null);
      }
      setIsVisible(true);
    }
  };

  const handleMouseLeave = () => {
    if (!isPinned) {
      // Add a small delay before hiding
      const timeout = setTimeout(() => {
        setIsVisible(false);
        setHideTimeout(null);
      }, 300);
      setHideTimeout(timeout);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!dashboardId) return;

    try {
      setIsDeleting(true);
      await deleteDashboard(dashboardId);
      onDeleted?.();
      setShowDeleteModal(false);
    } catch (error) {
      console.error('Error deleting dashboard:', error);
      // You could add a toast notification here
      alert(error instanceof Error ? error.message : 'خطا در حذف داشبورد');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleViewModeClick = async () => {
    try {
      if (isFullscreen) {
        // Exit fullscreen
        if (document.fullscreenElement) {
          await document.exitFullscreen();
        }
        onFullscreenChange?.(false);
      } else {
        // Enter fullscreen
        await document.documentElement.requestFullscreen();
        onFullscreenChange?.(true);
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error);
    }

    // Also call the original onViewMode if provided
    onViewMode?.();
  };

  const handleEditDashboard = () => {
    if (dashboardId) {
      navigate(`/dashboard/${dashboardId}/edit`);
    }
    // Also call the original onEditDashboard if provided
    onEditDashboard?.();
  };

  const toolbarItems = [
    {
      id: 'update-reports',
      label: 'به‌روزرسانی گزارش‌ها',
      icon: ArrowCounterClockwiseIcon,
      onClick: onUpdateReports,
    },
    {
      id: 'edit-dashboard',
      label: 'ویرایش داشبورد',
      icon: PencilIcon,
      onClick: handleEditDashboard,
    },

    {
      id: 'edit-layout',
      label: isEditMode ? (isUpdatingLayout ? 'در حال ذخیره...' : 'تایید چیدمان') : 'ویرایش چیدمان',
      icon: SquaresFourIcon,
      onClick: onEditLayout,
      disabled: isUpdatingLayout,
    },
    {
      id: 'view-mode',
      label: isFullscreen ? 'خروج از تمام صفحه' : 'حالت تمام صفحه',
      icon: isFullscreen ? CornersInIcon : CornersOutIcon,
      onClick: handleViewModeClick,
    },
    {
      id: 'share',
      label: 'اشتراک گذاری',
      icon: ShareNetworkIcon,
      onClick: onShare,
    },
    {
      id: 'user-access',
      label: 'دسترسی به کاربران',
      icon: UsersIcon,
      onClick: onUserAccess,
      variant: 'secondary' as const,
    },
    {
      id: 'delete',
      label: 'حذف داشبورد',
      icon: TrashIcon,
      onClick: dashboardId ? handleDeleteClick : onDelete,
    },

    {
      id: 'pin',
      label: isPinned ? 'آزاد کردن نوار ابزار' : 'پین کردن نوار ابزار',
      icon: isPinned ? PushPinSlashIcon : PushPinIcon,
      onClick: handlePinToggle,
      isActive: isPinned,
    },
  ];

  return (
    <div className="relative">
      {!isPinned && !isVisible && (
        <div
          className="absolute top-0 right-0 left-0 z-50 h-2 bg-transparent"
          onMouseEnter={handleMouseEnterHoverArea}
        />
      )}

      <div
        className={cn(
          'sticky top-0 z-40 overflow-hidden transition-all duration-400 ease-in-out',
          !isPinned && !isVisible ? 'max-h-0 opacity-0' : 'max-h-20 opacity-100'
        )}
        onMouseEnter={handleMouseEnterToolbar}
        onMouseLeave={handleMouseLeave}
      >
        <div
          className={cn(
            'flex items-center border-b border-neutral-700 bg-neutral-900 px-8 py-4 transition-all duration-400 ease-in-out',
            !isPinned && !isVisible
              ? '-translate-y-2 scale-98 transform opacity-0'
              : 'translate-y-0 scale-100 transform opacity-100',
            className
          )}
        >
          {toolbarItems.map((item, index) => {
            const IconComponent = item.icon;
            const isLast = index === toolbarItems.length - 1;

            return (
              <button
                key={item.id}
                onClick={item.onClick}
                disabled={!item.onClick || item.disabled}
                className={cn(
                  'flex cursor-pointer items-center gap-2 px-4 py-2 text-sm font-medium text-gray-300 transition-all',
                  !isPinned && !isVisible
                    ? 'scale-95 opacity-0'
                    : 'scale-100 opacity-100',
                  index > 0 && !isLast && 'border-r border-gray-300/40',
                  isLast && 'mr-auto',
                  item.disabled && 'opacity-50 cursor-not-allowed'
                )}
                style={{
                  transitionDuration:
                    !isPinned && !isVisible ? '250ms' : '300ms',
                  transitionDelay:
                    !isPinned && !isVisible
                      ? `${(toolbarItems.length - index - 1) * 25}ms`
                      : `${index * 50}ms`,
                }}
              >
                <IconComponent className="h-4 w-4" />
                <span>{item.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteConfirm}
        titleIcon={<TrashSimpleIcon size={32} weight="bold" />}
        confirmIcon={<TrashSimpleIcon className="h-4 w-4" weight="bold" />}
        variant="danger"
        title="آیا از حذف داشبورد اطمینان دارید؟"
        message="با حذف داشبورد امکان بازیابی و دسترسی مجدد به آن وجود نخواهد داشت."
        confirmText={isDeleting ? 'در حال حذف...' : 'حذف داشبورد'}
        cancelText="انصراف"
      />
    </div>
  );
};

export default DashboardToolbar;
