import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import Breadcrumb from '@/components/ui/Breadcrumb';
import SocialMediaSelect from '@/components/ui/SocialMediaSelect';
import { useState, useCallback } from 'react';
import Select, { SelectOption } from '@/components/ui/Select';
import TextInput from '@/components/ui/TextInput';
import Button from '@/components/ui/Button';
import Collapsible from '@/components/ui/Collapsible';
import SearchDrawerInput from '@/components/ui/SearchDrawerInput';
import TimePeriodPicker from '@/components/ui/TimePeriodPicker';
import TagInput from '@/components/ui/TagInput';
import SourceInput from '@/components/ui/SourceInput';
import { createWidget } from '@/services/dashboardService';
import { CreateWidgetPayload } from '@/types/dashboard';

// Define the mapping between report types and their allowed chart types
const REPORT_CHART_MAPPING = {
  process: ['bar_stack', 'bar_comp', 'line', 'table'],
  // process: ['line', 'table'],
  top_sources: ['table', 'bar_stack_hor', 'bar_stack_ver', 'radial'],
  sentiments: [
    'pie',
    'donut',
    'semi_pie',
    'bar_stack_hor',
    'bar_stack_ver',
    'table',
    'radar',
    'spider',
    'wind',
  ],
} as const;

// Define which chart types are stack charts (only for multiple socials)
const STACK_CHART_TYPES = [
  'bar_stack',
  'bar_comp',
  'bar_stack_hor',
  'bar_stack_ver',
] as const;

// Sample sources data (same as in Create.tsx)
const sampleSources = [
  {
    user_name: 'john_doe',
    user_title: 'جان دو',
    avatar: 'https://via.placeholder.com/40',
    followers_count: 1500,
    following_count: 300,
    posts_count: 120,
    bio: 'توسعه‌دهنده فرانت‌اند',
    verified: true,
    political_category: {
      label: 'اصلاح‌طلب',
      prob: {
        eslahtalab: 0.8,
        edalatkhah: 0.1,
        ahmadinezhad: 0.05,
        osoolgera: 0.03,
        saltanat: 0.01,
        monafegh: 0.005,
        barandaz: 0.003,
        restart: 0.002,
      },
      support_state: 'support',
    },
    gender: {
      label: 'male',
      prob: {
        female: 0.2,
        male: 0.8,
      },
    },
    age: {
      range: '25-35',
      label: '25-35 سال',
    },
    similar_accounts: null,
  },
  {
    user_name: 'jane_smith',
    user_title: 'جین اسمیت',
    avatar: 'https://via.placeholder.com/40',
    followers_count: 2500,
    following_count: 450,
    posts_count: 200,
    bio: 'طراح UI/UX',
    verified: false,
    political_category: {
      label: 'اصولگرا',
      prob: {
        eslahtalab: 0.1,
        edalatkhah: 0.15,
        ahmadinezhad: 0.1,
        osoolgera: 0.6,
        saltanat: 0.03,
        monafegh: 0.01,
        barandaz: 0.005,
        restart: 0.005,
      },
      support_state: 'neutral',
    },
    gender: {
      label: 'female',
      prob: {
        female: 0.9,
        male: 0.1,
      },
    },
    age: {
      range: '30-40',
      label: '30-40 سال',
    },
    similar_accounts: null,
  },
];

export default function CreateReport() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [selectedSocials, setSelectedSocials] = useState<number[]>(['twitter']);
  const [selectedReport, setSelectedReport] = useState<string>('');
  const [selectedChartType, setSelectedChartType] = useState<string>('');
  const [selectedTimeInterval, setSelectedTimeInterval] = useState<string>('');
  const [title, setTitle] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Advanced filters state
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const [timePeriodValue, setTimePeriodValue] = useState<number>(
    24 * 60 * 60 * 1000
  ); // 24 hours in milliseconds

  const breadcrumbItems = [
    { label: 'داشبورد', href: '/dashboard' },
    { label: 'داشبورد جزئیات', href: `/dashboard/${id}` },
    { label: 'ایجاد گزارش جدید' },
  ];

  // Define report options
  const reportOptions: SelectOption[] = [
    { label: 'روند انتشار محتوا طی زمان', value: 'process' },
    { label: 'برترین منابع (کاربران)', value: 'top_sources' },
    { label: 'تحلیل احساسات محتوای منتشر شده', value: 'sentiments' },
  ];

  // Define chart type options with Persian labels
  const chartTypeLabels: Record<string, string> = {
    bar_stack: 'نمودار میله‌ای انباشته',
    bar_comp: 'نمودار میله‌ای مقایسه‌ای',
    line: 'نمودار خطی',
    table: 'جدول',
    bar_stack_hor: 'نمودار میله‌ای انباشته افقی',
    bar_stack_ver: 'نمودار میله‌ای انباشته عمودی',
    radial: 'نمودار شعاعی',
    pie: 'نمودار دایره‌ای',
    donut: 'نمودار حلقه‌ای',
    semi_pie: 'نمودار نیم‌دایره',
    radar: 'نمودار راداری',
    spider: 'نمودار عنکبوتی',
    wind: 'نمودار بادی',
  };

  // Define time interval options (in minutes)
  const timeIntervalOptions: SelectOption[] = [
    { label: '1 دقیقه', value: '1' },
    { label: '3 دقیقه', value: '3' },
    { label: '5 دقیقه', value: '5' },
    { label: '15 دقیقه', value: '15' },
    { label: '30 دقیقه', value: '30' },
    { label: '45 دقیقه', value: '45' },
    { label: '60 دقیقه', value: '60' },
    { label: '90 دقیقه', value: '90' },
    { label: '120 دقیقه', value: '120' },
  ];

  // Generate chart type options based on selected report and selected socials
  const getChartTypeOptions = (): SelectOption[] => {
    if (!selectedReport || !(selectedReport in REPORT_CHART_MAPPING)) {
      return [];
    }

    const allowedChartTypes =
      REPORT_CHART_MAPPING[selectedReport as keyof typeof REPORT_CHART_MAPPING];

    // Filter out stack charts if only one social is selected
    const filteredChartTypes =
      selectedSocials.length === 1
        ? allowedChartTypes.filter(
            (chartType) =>
              !STACK_CHART_TYPES.includes(
                chartType as (typeof STACK_CHART_TYPES)[number]
              )
          )
        : allowedChartTypes;

    return filteredChartTypes.map((chartType) => ({
      label: chartTypeLabels[chartType],
      value: chartType,
    }));
  };

  const handleSocialMediaSelectChange = (selectedIds: number[]) => {
    setSelectedSocials(selectedIds);
    setError('');

    // Reset chart type if currently selected chart is no longer available
    if (selectedChartType && selectedReport) {
      const allowedChartTypes =
        REPORT_CHART_MAPPING[
          selectedReport as keyof typeof REPORT_CHART_MAPPING
        ];
      const filteredChartTypes =
        selectedIds.length === 1
          ? allowedChartTypes.filter(
              (chartType) =>
                !STACK_CHART_TYPES.includes(
                  chartType as (typeof STACK_CHART_TYPES)[number]
                )
            )
          : allowedChartTypes;

      if (
        !filteredChartTypes.includes(
          selectedChartType as (typeof filteredChartTypes)[number]
        )
      ) {
        setSelectedChartType('');
      }
    }
  };

  const handleReportChange = (value: string | string[]) => {
    const reportValue = Array.isArray(value) ? value[0] : value;
    setSelectedReport(reportValue);
    // Reset chart type when report changes
    setSelectedChartType('');
  };

  const handleChartTypeChange = (value: string | string[]) => {
    const chartValue = Array.isArray(value) ? value[0] : value;
    setSelectedChartType(chartValue);
  };

  const handleTimeIntervalChange = (value: string | string[]) => {
    const intervalValue = Array.isArray(value) ? value[0] : value;
    setSelectedTimeInterval(intervalValue);
  };

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
    setError('');
  };

  // Advanced filters handlers
  const handleSearchQueryChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
      setError('');
    },
    []
  );

  const handleHashtagsChange = useCallback((newTags: string[]) => {
    setHashtags(newTags);
  }, []);

  const handleSourcesChange = useCallback((newSources: string[]) => {
    setSelectedSources(newSources);
  }, []);

  const handleTimePeriodChange = useCallback((value: number) => {
    setTimePeriodValue(value);
  }, []);

  const validateForm = (): boolean => {
    if (!title.trim()) {
      setError('عنوان نمودار الزامی است');
      return false;
    }
    if (!selectedReport) {
      setError('انتخاب نوع گزارش الزامی است');
      return false;
    }
    if (!selectedChartType) {
      setError('انتخاب نوع نمودار الزامی است');
      return false;
    }
    if (!selectedTimeInterval) {
      setError('انتخاب وقفه زمانی الزامی است');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !id) return;

    setIsSubmitting(true);
    setError('');

    try {
      // Build query object with advanced filters
      const queryParams: any = {};

      if (searchQuery.trim()) {
        queryParams.q = searchQuery.trim();
      }

      if (hashtags.length > 0) {
        queryParams.hashtags = hashtags;
      }

      if (selectedSources.length > 0) {
        queryParams.sources = selectedSources;
      }

      const payload: CreateWidgetPayload = {
        title: title.trim(),
        chart_type: selectedChartType,
        report_type: selectedReport,
        params: {
          runtime: {
            interval: parseInt(selectedTimeInterval) * 60, // Convert minutes to seconds
            gap: timePeriodValue, // Add time period from advanced filters
          },
          position: {
            x: 0,
            y: 0,
            width: 5,
            height: 5,
          },
          query: {
            platform: selectedSocials,
            ...queryParams,
          },
          // // Add query params if any advanced filters are set
          // ...(Object.keys(queryParams).length > 0 && { query: queryParams }),
        },
      };

      await createWidget(id, payload);

      // Navigate back to dashboard detail page
      navigate(`/dashboard/${id}`);
    } catch (error) {
      console.error('Error creating widget:', error);
      setError(error instanceof Error ? error.message : 'خطا در ایجاد ویجت');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-full p-8">
      <Breadcrumb items={breadcrumbItems} />
      <div className="mx-auto mt-8 w-full max-w-7xl space-y-6">
        {/* Social Media Select - Full Width */}
        <div className="w-full">
          <SocialMediaSelect
            mode="multiple"
            label="بستر‌های مورد نظر خود را انتخاب کنید"
            value={selectedSocials}
            onChange={handleSocialMediaSelectChange}
            error={error}
          />
        </div>

        {/* Report Selection Section - Collapsible */}
        <Collapsible
          title="انتخاب نوع گزارش"
          defaultOpen={true}
          className="mt-8"
        >
          {/* Text Input and Time Interval Select - Half Width Each */}
          <div className="mb-6 flex items-center gap-4">
            <div className="flex-6">
              <TextInput
                label="عنوان نمودار"
                placeholder="نمودار برترین محتوا منتشر شده در اینستاگرام"
                value={title}
                onChange={handleTitleChange}
              />
            </div>
            <div className="flex-6">
              <Select
                label="وقفه زمانی به‌روزرسانی گزارش‌ها"
                placeholder="انتخاب کنید"
                options={timeIntervalOptions}
                value={selectedTimeInterval}
                onChange={handleTimeIntervalChange}
              />
            </div>
          </div>

          {/* Report and Chart Type Selects */}
          <div className="flex items-center gap-4">
            <div className="flex-6">
              <Select
                label="گزارش‌های آماری و هوش مصنوعی"
                placeholder="انتخاب نوع گزارش"
                options={reportOptions}
                value={selectedReport}
                onChange={handleReportChange}
              />
            </div>
            <div className="flex-6">
              <Select
                label="نوع نمودار"
                placeholder={
                  selectedReport
                    ? 'انتخاب نوع نمودار'
                    : 'ابتدا نوع گزارش را انتخاب کنید'
                }
                options={getChartTypeOptions()}
                value={selectedChartType}
                onChange={handleChartTypeChange}
              />
            </div>
          </div>
        </Collapsible>

        {/* Advanced Filters Section - Collapsible */}
        <Collapsible
          title="فیلترهای پیشرفته"
          defaultOpen={false}
          className="mt-8"
        >
          <SearchDrawerInput
            label="عبارت جستجو"
            name="searchQuery"
            placeholder="عبارت جستجو خود را وارد کنید"
            value={searchQuery}
            onChange={handleSearchQueryChange}
          />

          <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2">
            <TagInput
              label="هشتگ‌ها"
              mode="scroll"
              name="hashtags"
              value={hashtags}
              onChange={handleHashtagsChange}
              placeholder="هشتگ‌های مورد نظر را وارد کنید"
            />

            <SourceInput
              label="انتخاب منابع"
              value={selectedSources}
              onChange={handleSourcesChange}
              placeholder="منابع مورد نظر خود را انتخاب کنید"
              data={sampleSources}
              quickSelectCount={5}
            />
          </div>

          <TimePeriodPicker
            value={timePeriodValue}
            onChange={handleTimePeriodChange}
            label="بازه زمانی نتایج گزارش"
          />
        </Collapsible>

        {/* Error Display */}
        {error && (
          <div className="mt-4 rounded-md bg-red-50 p-4">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <div className="mt-8 flex justify-end gap-4">
          <Button
            variant="secondary"
            onClick={() => navigate(`/dashboard/${id}`)}
            disabled={isSubmitting}
          >
            انصراف
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'در حال ایجاد...' : 'ایجاد ویجت'}
          </Button>
        </div>
      </div>
    </div>
  );
}
