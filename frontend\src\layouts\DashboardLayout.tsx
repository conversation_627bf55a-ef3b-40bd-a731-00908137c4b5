import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import { Outlet } from 'react-router-dom';
import React from 'react';

export const DashboardLayout: React.FC = () => {
  return (
    <div className="flex min-h-screen bg-neutral-800">
      <Sidebar />
      <div className="flex w-full flex-col lg:mr-64">
        <Header />
        <main className="flex-1 overflow-auto bg-[#262626] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-neutral-600">
          <div className="min-h-full">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};
