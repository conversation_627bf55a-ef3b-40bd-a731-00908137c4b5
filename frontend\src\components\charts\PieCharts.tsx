import React from 'react';
import Responsive<PERSON>hartWrapper from './ResponsiveChartWrapper';
import { createChartConfig, RESPONSIVE_RULES } from './chartConfig';
import Highcharts from 'highcharts';
import HighchartsTreeMap from 'highcharts/modules/treemap';

HighchartsTreeMap(Highcharts);

// Types for pie chart data
export interface PieChartData {
  name: string;
  y: number;
  color?: string;
}

export interface PieChartProps {
  data?: PieChartData[];
  title?: string;
  className?: string;
  showLegend?: boolean;
  innerSize?: string | number;
  startAngle?: number;
  endAngle?: number;
  showDataLabels?: boolean;
}

/**
 * نمودار دایره‌ای کلاسیک
 */
export const PieChart: React.FC<PieChartProps> = ({
  data = [
    { name: 'تلگرام', y: 45.2 },
    { name: 'اینستاگرام', y: 28.7 },
    { name: 'توییتر', y: 15.3 },
    { name: 'یوتوب', y: 10.8 },
  ],
  title = 'نمودار دایره‌ای',
  className = '',
  showLegend = true,
  showDataLabels = true,
}) => {
  const options = createChartConfig(
    {
      chart: {
        type: 'pie',
      },
      title: {
        text: title,
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: showDataLabels,
            format: '<b>{point.name}</b>: {point.percentage:.1f}%',
            style: {
              color: '#ffffff',
              fontSize: '11px',
              fontFamily: 'IranYekanX',
              textOutline: 'none',
            },
            distance: 20,
          },
          showInLegend: showLegend,
          borderWidth: 0,
          shadow: false,
        },
      },
      series: [
        {
          type: 'pie',
          name: 'درصد',
          data: data,
        },
      ],
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

/**
 * نمودار دونات (دایره‌ای با مرکز خالی)
 */
export const DonutChart: React.FC<PieChartProps> = ({
  data = [
    { name: 'موبایل', y: 61.41 },
    { name: 'دسکتاپ', y: 11.84 },
    { name: 'تبلت', y: 10.85 },
    { name: 'سایر', y: 15.9 },
  ],
  title = 'نمودار دونات',
  className = '',
  showLegend = true,
  innerSize = '50%',
  showDataLabels = true,
}) => {
  const options = createChartConfig(
    {
      chart: {
        type: 'pie',
      },
      title: {
        text: title,
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          innerSize: innerSize,
          dataLabels: {
            enabled: showDataLabels,
            format: '<b>{point.name}</b>: {point.percentage:.1f}%',
            style: {
              color: '#ffffff',
              fontSize: '11px',
              fontFamily: 'IranYekanX',
              textOutline: 'none',
            },
            distance: 20,
          },
          showInLegend: showLegend,
          borderWidth: 0,
          shadow: false,
        },
      },
      series: [
        {
          type: 'pie',
          name: 'درصد',
          data: data,
        },
      ],
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

/**
 * نمودار نیم دایره
 */
export const SemiCircleChart: React.FC<PieChartProps> = ({
  data = [
    { name: 'کم', y: 25 },
    { name: 'متوسط', y: 35 },
    { name: 'زیاد', y: 40 },
  ],
  title = 'نمودار نیم دایره',
  className = '',
  showLegend = true,
  startAngle = -90,
  endAngle = 90,
  innerSize = '50%',
  showDataLabels = true,
}) => {
  const options = createChartConfig(
    {
      chart: {
        type: 'pie',
      },
      title: {
        text: title,
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          startAngle: startAngle,
          endAngle: endAngle,
          innerSize: innerSize,
          dataLabels: {
            enabled: showDataLabels,
            format: '<b>{point.name}</b>: {point.percentage:.1f}%',
            style: {
              color: '#ffffff',
              fontSize: '11px',
              fontFamily: 'IranYekanX',
              textOutline: 'none',
            },
            distance: 15,
          },
          showInLegend: showLegend,
          borderWidth: 0,
          shadow: false,
          center: ['50%', '75%'],
        },
      },
      series: [
        {
          type: 'pie',
          name: 'درصد',
          data: data,
        },
      ],
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

/**
 * نمودار بلوکی (Treemap)
 */
export const BlockChart: React.FC<{
  data?: Array<{
    name: string;
    value: number;
    colorValue?: number;
  }>;
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = ({
  data = [
    { name: 'تلگرام', value: 6, colorValue: 1 },
    { name: 'اینستاگرام', value: 6, colorValue: 2 },
    { name: 'توییتر', value: 4, colorValue: 3 },
    { name: 'یوتوب', value: 3, colorValue: 4 },
    { name: 'لینکدین', value: 2, colorValue: 5 },
    { name: 'فیسبوک', value: 2, colorValue: 6 },
    { name: 'سایر', value: 1, colorValue: 7 },
  ],
  title = 'نمودار بلوکی',
  className = '',
  showLegend = false,
}) => {
  const options = createChartConfig(
    {
      chart: {
        type: 'treemap',
      },
      title: {
        text: title,
      },
      plotOptions: {
        treemap: {
          layoutAlgorithm: 'squarified',
          dataLabels: {
            enabled: true,
            format: '{point.name}<br>{point.value}',
            style: {
              color: '#ffffff',
              fontSize: '11px',
              fontFamily: 'IranYekanX',
              textOutline: '1px contrast',
            },
          },
          borderWidth: 2,
          borderColor: '#ffffff',
          shadow: false,
        },
      },
      series: [
        {
          type: 'treemap',
          name: 'مقدار',
          data: data.map((item) => ({
            name: item.name,
            value: item.value,
            colorValue: item.colorValue,
          })),
        },
      ],
      colorAxis: {
        minColor: '#004748',
        maxColor: '#5BF7FA',
      },
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};
