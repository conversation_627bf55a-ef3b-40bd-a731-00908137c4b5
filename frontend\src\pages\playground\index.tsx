import { cn } from '@/utils/utlis';
import { useState } from 'react';
import DateTimePicker from '@/components/ui/DateTimePicker';
import Select from '@/components/ui/Select';
import { socialItems } from '@/constants/socialMedia';

export default function Page() {
  const [selectedSocials, setSelectedSocials] = useState<number[]>([1]);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    startDate: '',
    endDate: '',
    updateInterval: '',
    keywords: [] as string[],
    hashtags: [] as string[],
  });

  const handleDateChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const updateIntervalOptions = [
    { value: '1h', label: 'هر یک ساعت' },
    { value: '2h', label: 'هر دو ساعت' },
    { value: '6h', label: 'هر شش ساعت' },
    { value: '12h', label: 'هر دوازده ساعت' },
    { value: '24h', label: 'هر بیست و چهار ساعت' },
  ];

  const handleSelectChange = (name: string, value: string | string[]) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div>
      <DateTimePicker
        label="تاریخ و زمان شروع"
        value={formData.startDate}
        onChange={(value) => handleDateChange('startDate', value)}
      />
      <Select
        label="وقفه زمانی به‌روزرسانی گزارش‌ها"
        options={updateIntervalOptions}
        value={formData.updateInterval}
        onChange={(value) => handleSelectChange('updateInterval', value)}
      />
      <div className="text-lg font-medium text-stone-400">
        بستر‌های مورد نظر خود را انتخاب کنید
      </div>
      <div className="flex w-full flex-wrap justify-center gap-5">
        {socialItems.map((item) => {
          const Icon = item.icon;
          return (
            <div
              key={item.id}
              className={cn(
                'flex h-[175px] w-[175px] shrink-0 cursor-pointer flex-col items-center justify-between rounded-lg border-2 bg-neutral-900 p-6 text-center shadow-lg transition-colors select-none sm:w-[calc(50%-0.625rem)] md:w-[175px]',
                selectedSocials.indexOf(item.id) === -1
                  ? 'border-neutral-700'
                  : 'border-primary-400'
              )}
              onClick={() => {
                if (
                  selectedSocials.length === 1 &&
                  selectedSocials.includes(item.id)
                ) {
                  return;
                }
                setSelectedSocials((prev) =>
                  prev.includes(item.id)
                    ? prev.filter((socialItem) => socialItem !== item.id)
                    : [...prev, item.id]
                );
              }}
            >
              <Icon size={46} className="fill-primary-400" />
              <div className="text-lg font-bold text-white">{item.title}</div>
              <div className="text-sm font-bold text-neutral-500">
                {item.description}
              </div>
            </div>
          );
        })}
      </div>
      <div className="mx-auto p-6"></div>
    </div>
  );
}
