import {
  InstagramLogoIcon,
  RssIcon,
  TelegramLogoIcon,
  TwitterLogoIcon,
  YoutubeLogoIcon,
} from '@phosphor-icons/react';

export type SocialItem = {
  id: number;
  title: string;
  description: string;
  icon: React.ComponentType<{ size: number; className: string }>;
};

export const socialItems: SocialItem[] = [
  {
    id: 3,
    value: 'twitter',
    title: 'توییتر',
    description: 'تحلیل داده‌های توییتر  (X)',
    icon: TwitterLogoIcon,
  },
  {
    id: 1,
    value: 'telegram',
    title: 'تلگرام',
    description: 'تحلیل داده‌هـای\nپیام رسان تلگرام',
    icon: TelegramLogoIcon,
  },
  {
    id: 2,
    value: 'instagram',
    title: 'اینستاگرام',
    description: 'تحلیل داده‌های اینستاگرام',
    icon: InstagramLogoIcon,
  },
  {
    id: 4,
    value: 'news',
    title: 'سایت‌های خبری',
    description: 'تحلیل داده‌های سایت‌های خبری',
    icon: RssIcon,
  },
];
