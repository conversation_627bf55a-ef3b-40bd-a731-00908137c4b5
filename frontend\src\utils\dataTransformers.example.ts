/**
 * مثال‌های کاربردی برای استفاده از سیستم تبدیل داده‌ها
 * 
 * این فایل نمونه‌هایی از نحوه استفاده از DataTransformerFactory
 * برای تبدیل انواع مختلف داده‌های API به فرمت مناسب نمودارها ارائه می‌دهد.
 */

import { DataTransformerFactory } from './dataTransformers';

// مثال 1: تبدیل داده‌های process برای نمودار خطی
export function exampleProcessLineChart() {
  const apiResponse = {
    status: "OK",
    code: 200,
    message: "بازیابی اطلاعات با موفقیت انجام شد",
    data: {
      twitter: [
        {
          datetime: "2025-07-01T00:00:00.000+03:30",
          timestamp: 1751315400000,
          count: 30
        },
        {
          datetime: "2025-07-02T00:00:00.000+03:30",
          timestamp: 1751401800000,
          count: 61
        },
        {
          datetime: "2025-07-03T00:00:00.000+03:30",
          timestamp: 1751488200000,
          count: 45
        }
      ]
    }
  };

  // تبدیل داده برای نمودار خطی
  const chartData = DataTransformerFactory.transform(
    apiResponse.data,
    'process',
    'line'
  );

  console.log('Process Line Chart Data:', chartData);
  /*
  خروجی:
  {
    series: [{
      name: 'تعداد',
      data: [[1751315400000, 30], [1751401800000, 61], [1751488200000, 45]]
    }],
    categories: ['۱ تیر', '۲ تیر', '۳ تیر']
  }
  */
}

// مثال 2: تبدیل داده‌های احساسات برای نمودار دایره‌ای
export function exampleSentimentsPieChart() {
  const apiResponse = {
    status: "OK",
    code: 200,
    message: "تحلیل احساسات انجام شد",
    data: [
      {
        sentiment: 'positive',
        count: 120,
        percentage: 60
      },
      {
        sentiment: 'negative',
        count: 50,
        percentage: 25
      },
      {
        sentiment: 'neutral',
        count: 30,
        percentage: 15
      }
    ]
  };

  // تبدیل داده برای نمودار دایره‌ای
  const chartData = DataTransformerFactory.transform(
    apiResponse.data,
    'sentiments',
    'pie'
  );

  console.log('Sentiments Pie Chart Data:', chartData);
  /*
  خروجی:
  {
    series: [{
      name: 'احساسات',
      data: [
        { name: 'مثبت', y: 120 },
        { name: 'منفی', y: 50 },
        { name: 'خنثی', y: 30 }
      ]
    }]
  }
  */
}

// مثال 3: تبدیل داده‌های منابع برای جدول
export function exampleSourceInfoTable() {
  const apiResponse = {
    status: "OK",
    code: 200,
    message: "اطلاعات منابع دریافت شد",
    data: [
      {
        source: 'Twitter',
        count: 1500,
        percentage: 45
      },
      {
        source: 'Instagram',
        count: 1200,
        percentage: 36
      },
      {
        source: 'Telegram',
        count: 633,
        percentage: 19
      }
    ]
  };

  // تبدیل داده برای جدول
  const chartData = DataTransformerFactory.transform(
    apiResponse.data,
    'source_info',
    'table'
  );

  console.log('Source Info Table Data:', chartData);
  /*
  خروجی:
  {
    data: [
      { منبع: 'Twitter', تعداد: 1500, درصد: '45%' },
      { منبع: 'Instagram', تعداد: 1200, درصد: '36%' },
      { منبع: 'Telegram', تعداد: 633, درصد: '19%' }
    ]
  }
  */
}

// مثال 4: تبدیل داده‌های ابر کلمات
export function exampleWordCloud() {
  const apiResponse = {
    status: "OK",
    code: 200,
    message: "ابر کلمات تولید شد",
    data: [
      { text: 'انتخابات', weight: 100 },
      { text: 'سیاست', weight: 85 },
      { text: 'اقتصاد', weight: 70 },
      { text: 'ورزش', weight: 60 },
      { text: 'فناوری', weight: 45 }
    ]
  };

  // تبدیل داده برای ابر کلمات
  const chartData = DataTransformerFactory.transform(
    apiResponse.data,
    'cloud',
    'cloud'
  );

  console.log('Word Cloud Data:', chartData);
  /*
  خروجی:
  {
    data: [
      { name: 'انتخابات', weight: 100 },
      { name: 'سیاست', weight: 85 },
      { name: 'اقتصاد', weight: 70 },
      { name: 'ورزش', weight: 60 },
      { name: 'فناوری', weight: 45 }
    ]
  }
  */
}

// مثال 5: تبدیل داده‌های چندمنبعه برای نمودار خطی
export function exampleMultiSourceLineChart() {
  const apiResponse = {
    status: "OK",
    code: 200,
    message: "داده‌های پیشرفته دریافت شد",
    data: {
      twitter: [
        { datetime: "2025-07-01T00:00:00.000+03:30", timestamp: 1751315400000, count: 30 },
        { datetime: "2025-07-02T00:00:00.000+03:30", timestamp: 1751401800000, count: 61 }
      ],
      instagram: [
        { datetime: "2025-07-01T00:00:00.000+03:30", timestamp: 1751315400000, count: 25 },
        { datetime: "2025-07-02T00:00:00.000+03:30", timestamp: 1751401800000, count: 45 }
      ],
      telegram: [
        { datetime: "2025-07-01T00:00:00.000+03:30", timestamp: 1751315400000, count: 15 },
        { datetime: "2025-07-02T00:00:00.000+03:30", timestamp: 1751401800000, count: 35 }
      ]
    }
  };

  // تبدیل داده برای نمودار خطی چندمنبعه
  const chartData = DataTransformerFactory.transform(
    apiResponse.data,
    'advance',
    'line'
  );

  console.log('Multi-Source Line Chart Data:', chartData);
  /*
  خروجی:
  {
    series: [
      { name: 'twitter', data: [30, 61] },
      { name: 'instagram', data: [25, 45] },
      { name: 'telegram', data: [15, 35] }
    ],
    categories: ['۱ تیر', '۲ تیر']
  }
  */
}

// مثال 6: تبدیل داده‌های آماری برای نمایش تک مقدار
export function exampleStatisticalBadge() {
  const apiResponse = {
    status: "OK",
    code: 200,
    message: "آمار کلی محاسبه شد",
    data: [
      {
        metric: 'total_posts',
        value: 15420,
        change: 12.5,
        changeType: 'increase'
      }
    ]
  };

  // تبدیل داده برای نمایش تک مقدار
  const chartData = DataTransformerFactory.transform(
    apiResponse.data,
    'statistical',
    'badge'
  );

  console.log('Statistical Badge Data:', chartData);
  /*
  خروجی:
  {
    value: 15420
  }
  */
}

// مثال 7: تبدیل داده‌های پست‌ها برای لیست
export function examplePostsList() {
  const apiResponse = {
    status: "OK",
    code: 200,
    message: "پست‌ها دریافت شد",
    data: [
      {
        id: '1',
        content: 'این یک پست نمونه است که محتوای طولانی‌تری دارد و باید کوتاه شود...',
        author: 'کاربر۱',
        datetime: '2025-07-01T10:30:00.000+03:30',
        source: 'Twitter',
        engagement: 150
      },
      {
        id: '2',
        content: 'پست دوم با محتوای متفاوت',
        author: 'کاربر۲',
        datetime: '2025-07-01T11:15:00.000+03:30',
        source: 'Instagram',
        engagement: 89
      }
    ]
  };

  // تبدیل داده برای لیست
  const chartData = DataTransformerFactory.transform(
    apiResponse.data,
    'posts',
    'list'
  );

  console.log('Posts List Data:', chartData);
  /*
  خروجی:
  {
    data: [
      {
        title: 'این یک پست نمونه است که محتوای طولانی‌تری دارد و باید کوتاه شود...',
        author: 'کاربر۱',
        date: '۱ تیر',
        source: 'Twitter',
        engagement: 150
      },
      {
        title: 'پست دوم با محتوای متفاوت',
        author: 'کاربر۲',
        date: '۱ تیر',
        source: 'Instagram',
        engagement: 89
      }
    ]
  }
  */
}

// تابع نمایشی برای اجرای تمام مثال‌ها
export function runAllExamples() {
  console.log('=== مثال‌های سیستم تبدیل داده ===\n');
  
  console.log('1. نمودار خطی فرآیند:');
  exampleProcessLineChart();
  
  console.log('\n2. نمودار دایره‌ای احساسات:');
  exampleSentimentsPieChart();
  
  console.log('\n3. جدول اطلاعات منابع:');
  exampleSourceInfoTable();
  
  console.log('\n4. ابر کلمات:');
  exampleWordCloud();
  
  console.log('\n5. نمودار خطی چندمنبعه:');
  exampleMultiSourceLineChart();
  
  console.log('\n6. نمایش تک مقدار آماری:');
  exampleStatisticalBadge();
  
  console.log('\n7. لیست پست‌ها:');
  examplePostsList();
}
