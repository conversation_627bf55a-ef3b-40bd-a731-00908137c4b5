import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import CustomGridDashboard from '@/pages/dashboard/components/CustomGridDashboard';
import Breadcrumb from '@/components/ui/Breadcrumb';
import Button from '@/components/ui/Button';
import DashboardToolbar from '@/components/ui/DashboardToolbar';
import { ConfirmModal } from '@/components/ui/ConfirmModal';
import { Toast } from '@/components/ui/Toast';
import { getDashboardById, updateWidgetPositions } from '@/services/dashboardService';
import { Dashboard } from '@/types/dashboard';
import { PlusIcon } from 'lucide-react';
import { CheckIcon } from '@heroicons/react/24/outline';

export default function Page() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [dashboard, setDashboard] = useState<Dashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingChanges, setPendingChanges] = useState(false);
  const [resetLayout, setResetLayout] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isUpdatingLayout, setIsUpdatingLayout] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error'>('success');
  const getCurrentLayoutRef = useRef<(() => Record<string, { x: number; y: number; width: number; height: number }>) | null>(null);

  useEffect(() => {
    const fetchDashboard = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const data = await getDashboardById(id);
        console.log('Dashboard data received:', data);
        console.log('Dashboard widgets:', data.widgets);
        setDashboard(data);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'خطا در دریافت اطلاعات داشبورد';
        setError(errorMessage);
        console.error('Error fetching dashboard:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboard();
  }, [id]);

  // Handle fullscreen change events (including ESC key)
  useEffect(() => {
    const handleFullscreenChange = () => {
      const isCurrentlyFullscreen = Boolean(document.fullscreenElement);
      setIsFullscreen(isCurrentlyFullscreen);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  const handleAddReport = () => {
    if (id) {
      navigate(`/dashboard/${id}/create-report`);
    }
  };

  const handleEditModeToggle = async () => {
    if (isEditMode && pendingChanges) {
      setShowConfirmModal(true);
    } else if (isEditMode && !pendingChanges) {
      // Exit edit mode without changes - no need to reset
      setIsEditMode(false);
      setPendingChanges(false);
    } else {
      // Enter edit mode
      setIsEditMode(true);
      setPendingChanges(false);
    }
  };

  const handleSaveLayout = async () => {
    if (!id || !getCurrentLayoutRef.current) return;

    try {
      setIsUpdatingLayout(true);

      // Get current layout from CustomGridDashboard
      const currentLayout = getCurrentLayoutRef.current();

      // Send layout to backend
      await updateWidgetPositions(id, currentLayout);

      // Show success message
      setToastType('success');
      setToastMessage('چیدمان با موفقیت ذخیره شد');
      setShowToast(true);

      // Exit edit mode
      setIsEditMode(false);
      setPendingChanges(false);
      setShowConfirmModal(false);

    } catch (error) {
      console.error('Error saving layout:', error);

      // Show error message
      setToastType('error');
      setToastMessage(error instanceof Error ? error.message : 'خطا در ذخیره چیدمان');
      setShowToast(true);
    } finally {
      setIsUpdatingLayout(false);
    }
  };

  const handleConfirmSave = () => {
    handleSaveLayout();
  };

  const handleCancelEdit = () => {
    // Reset layout to original state
    setResetLayout(true);
    setTimeout(() => setResetLayout(false), 100); // Reset the flag after a short delay

    setIsEditMode(false);
    setPendingChanges(false);
    setShowConfirmModal(false);
  };

  const handleLayoutChange = () => {
    setPendingChanges(true);
  };

  const handleDashboardDeleted = () => {
    // Navigate back to dashboard list after successful deletion
    navigate('/dashboard');
  };

  const handleFullscreenChange = (fullscreen: boolean) => {
    setIsFullscreen(fullscreen);
  };

  const breadcrumbItems = [
    { label: 'خانه', href: '/dashboard' },
    { label: dashboard?.title || 'داشبورد' },
  ];

  if (loading) {
    return (
      <div className="min-h-full p-8">
        <div className="flex justify-center">
          <div className="text-white">در حال بارگذاری...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-full p-8">
        <div className="flex justify-center">
          <div className="text-red-400">{error}</div>
        </div>
      </div>
    );
  }

  // Render fullscreen mode
  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 overflow-auto bg-[#262626]">
        <DashboardToolbar
          onEditLayout={handleEditModeToggle}
          isEditMode={isEditMode}
          dashboardId={id}
          onDeleted={handleDashboardDeleted}
          isFullscreen={isFullscreen}
          onFullscreenChange={handleFullscreenChange}
          isUpdatingLayout={isUpdatingLayout}
        />

        <div className="p-8">
          <CustomGridDashboard
            isEditMode={isEditMode}
            onLayoutChange={handleLayoutChange}
            resetLayout={resetLayout}
            onGetCurrentLayout={getCurrentLayoutRef}
            widgets={dashboard?.widgets || []}
            dashboard={dashboard}
          />
        </div>

        <ConfirmModal
          isOpen={showConfirmModal}
          onClose={handleCancelEdit}
          onConfirm={handleConfirmSave}
          variant="primary"
          title="تایید ذخیره تغییرات"
          message="آیا می‌خواهید تغییرات اعمال شده در چیدمان نمودارها را ذخیره کنید؟"
          confirmText={isUpdatingLayout ? 'در حال ذخیره...' : 'ذخیره تغییرات'}
          cancelText="انصراف"
          titleIcon={<CheckIcon className="h-8 w-8" />}
          confirmIcon={<CheckIcon className="h-4 w-4" />}
        />

        <Toast
          type={toastType}
          title={toastType === 'success' ? 'موفقیت' : 'خطا'}
          message={toastMessage}
          isVisible={showToast}
          onClose={() => setShowToast(false)}
          position="top-right"
          duration={4000}
        />
      </div>
    );
  }

  // Normal mode (within layout)
  return (
    <div className="min-h-full">
      <DashboardToolbar
        onEditLayout={handleEditModeToggle}
        isEditMode={isEditMode}
        dashboardId={id}
        onDeleted={handleDashboardDeleted}
        isFullscreen={isFullscreen}
        onFullscreenChange={handleFullscreenChange}
        isUpdatingLayout={isUpdatingLayout}
      />

      <div className="p-8">
        <div className="mb-6 flex items-center justify-between">
          <Breadcrumb items={breadcrumbItems} />

          <Button
            variant="primary"
            icon={<PlusIcon className="h-4 w-4" />}
            onClick={handleAddReport}
            className="flex items-center gap-2"
          >
            افزودن گزارش به داشبورد
          </Button>
        </div>

        <CustomGridDashboard
          isEditMode={isEditMode}
          onLayoutChange={handleLayoutChange}
          resetLayout={resetLayout}
          onGetCurrentLayout={getCurrentLayoutRef}
          widgets={dashboard?.widgets || []}
          dashboard={dashboard}
        />
      </div>

      <ConfirmModal
        isOpen={showConfirmModal}
        onClose={handleCancelEdit}
        onConfirm={handleConfirmSave}
        variant="primary"
        title="تایید ذخیره تغییرات"
        message="آیا می‌خواهید تغییرات اعمال شده در چیدمان نمودارها را ذخیره کنید؟"
        confirmText={isUpdatingLayout ? 'در حال ذخیره...' : 'ذخیره تغییرات'}
        cancelText="انصراف"
        titleIcon={<CheckIcon className="h-8 w-8" />}
        confirmIcon={<CheckIcon className="h-4 w-4" />}
      />

      <Toast
        type={toastType}
        title={toastType === 'success' ? 'موفقیت' : 'خطا'}
        message={toastMessage}
        isVisible={showToast}
        onClose={() => setShowToast(false)}
        position="top-right"
        duration={4000}
      />
    </div>
  );
}
