import Stepper from '@/components/ui/Stepper';
import { useState, useCallback, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import Breadcrumb from '@/components/ui/Breadcrumb';
import TextInput from '@/components/ui/TextInput';
import TextArea from '@/components/ui/TextArea';
import Button from '@/components/ui/Button';
import { FileText } from 'lucide-react';
import { CheckIcon } from '@phosphor-icons/react';
import { motion, AnimatePresence } from 'framer-motion';
import SearchDrawerInput from '@/components/ui/SearchDrawerInput';
import TimePeriodPicker from '@/components/ui/TimePeriodPicker';
import * as z from 'zod';
import TagInput from '@/components/ui/TagInput';
import { createDashboard } from '@/services/dashboardService';
import { CreateDashboardPayload, Dashboard } from '@/types/dashboard';
import SourceInput from '@/components/ui/SourceInput';

interface Form2 {
  params: {
    runtime: {
      gap: number;
    };
    query: {
      q: string;
      report_type?: string;
      hashtags?: string[];
      sources?: string[];
    };
  };
}

type PoliticalCategory = {
  label: string;
  prob: {
    eslahtalab: number;
    edalatkhah: number;
    ahmadinezhad: number;
    osoolgera: number;
    saltanat: number;
    monafegh: number;
    barandaz: number;
    restart: number;
  };
  support_state: string | null;
};

type Gender = {
  label: string;
  prob: {
    female: number;
    male: number;
  };
};

type Age = {
  range: string;
  label: string;
};

type TwitterProfile = {
  id: string;
  user_title: string;
  user_name: string;
  avatar: string;
  original_avatar: string | null;
  banner: string | null;
  bio: string;
  website: string | null;
  location: string | null;
  ai_location: string | null;
  birthday: string | null;
  join_date: string | null;
  tweet_count: number | null;
  following_count: number;
  follower_count: number;
  ai_summary: string | null;
  political_category: PoliticalCategory;
  gender: Gender;
  age: Age;
  similar_accounts: any;
};

// Sample source data
const sampleSources: TwitterProfile[] = [
  {
    id: '728252911675944961',
    user_title: 'ایران اینترنشنال',
    user_name: 'IranIntl',
    avatar: 'https://s3.synappse.ir/twitter/profiles/728252911675944961.jpg',
    original_avatar:
      'https://pbs.twimg.com/profile_images/1380225668428992513/midYaSKA_normal.jpg',
    banner:
      'https://pbs.twimg.com/profile_banners/728252911675944961/**********',
    bio: '‏‏‏‏‎‎‎‎#ایران_اینترنشنال تنها شبکه خبری ۲۴ ساعته فارسی‌زبان که تازه‌ترین خبرها را در سریع‌ترین زمان پوشش می‌دهد.\n📲 **************\n📧 <EMAIL>',
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 58,
    follower_count: 2099784,
    ai_summary:
      'این کاربر توئیتر به نام "ایران اینترنشنال" با عنوان "ایران اینترنشنال" و نام کاربری "ایران اینترنشنال"، یک حساب کاربری فعال در زمینه اخبار و تحلیل‌های سیاسی، اقتصادی و امنیتی است. او عمدتاً در مورد مسائل داخلی و خارجی ایران، از جمله روابط ایران با سایر کشورها، تحولات سیاسی و اقتصادی، و مسائل امنیتی و دفاعی می‌نویسد. پست‌های او اغلب شامل لینک‌های خبری و分析‌های chuyên sâu هستند و نشان می‌دهند که او به دنبال ارائه اطلاعات جامع و به‌روز در مورد مسائل ایران و جهان است.',
    political_category: {
      label: 'barandaz',
      prob: {
        eslahtalab: 0.03,
        edalatkhah: 0.01,
        ahmadinezhad: 0.02,
        osoolgera: 0.01,
        saltanat: 0.09,
        monafegh: 0.08,
        barandaz: 0.74,
        restart: 0.03,
      },
      support_state: 'opposite',
    },
    gender: {
      label: 'female',
      prob: {
        female: 0,
        male: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
  {
    id: '********',
    user_title: 'بپاخیزید ملت ایران،خدا با ماست',
    user_name: 'Bita842237181',
    avatar: 'https://s3.synappse.ir/twitter/profiles/********.jpg',
    original_avatar: '',
    banner: 'https://pbs.twimg.com/profile_banners/********/**********',
    bio: "Author, 'You'd Better Put Some Ice On That' retired RN & business owner, Speaker. 🇺🇸🇺🇸",
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 11905,
    follower_count: 1786923,
    ai_summary: null,
    political_category: {
      label: 'saltanat',
      prob: {
        eslahtalab: 0,
        saltanat: 0.96,
        barandaz: 0.02,
        monafegh: 0.01,
        edalatkhah: 0,
        osoolgera: 0,
        ahmadinezhad: 0,
        restart: 0,
      },
      support_state: 'opposite',
    },
    gender: {
      label: 'female',
      prob: {
        male: 0,
        female: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
  {
    id: '1005185803172106241',
    user_title: 'ایران اینترنشنال - خبر فوری',
    user_name: 'IranIntlbrk',
    avatar: 'https://s3.synappse.ir/twitter/profiles/1005185803172106241.jpg',
    original_avatar:
      'https://pbs.twimg.com/profile_images/1380229333277671429/wcsqRU42_normal.jpg',
    banner:
      'https://pbs.twimg.com/profile_banners/1005185803172106241/**********',
    bio: '‏حساب خبر فوری و تازه‌ترین اخبار تلویزیون ‎#ایران_اینترنشنال.\nحساب اصلی ایران اینترنشنال: ‎@IranIntl',
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 10,
    follower_count: 1701030,
    ai_summary:
      'این کاربر توئیتر به نام "ایران اینترنشنال - خبر فوری" علاقه زیادی به اخبار سیاسی، امنیتی و دفاعی دارد. اکثر پست‌های او مربوط به اخبار جاری در مورد اوکراین، روسیه، اسرائیل، ایران و سایر کشورها است. او به طور منظم در مورد موضوعاتی مانند درگیری‌ها، تحریم‌ها، و روابط بین المللی توئیت می‌کند. همچنین، او به اخبار داخلی ایران مانند اخبار مربوط به قوه قضاییه، مجلس و بانک مرکزی نیز علاقه نشان می‌دهد. در کل، این کاربر به عنوان یک منبع خبری در مورد مسائل سیاسی و امنیتی به نظر می‌رسد.',
    political_category: {
      label: 'barandaz',
      prob: {
        eslahtalab: 0.04,
        edalatkhah: 0.01,
        ahmadinezhad: 0.03,
        osoolgera: 0.01,
        saltanat: 0.08,
        monafegh: 0.11,
        barandaz: 0.68,
        restart: 0.04,
      },
      support_state: 'opposite',
    },
    gender: {
      label: 'female',
      prob: {
        female: 0,
        male: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
  {
    id: '1232772548540162049',
    user_title: 'ایران بالعربیه',
    user_name: 'iraninarabic_ir',
    avatar: 'https://s3.synappse.ir/twitter/profiles/1232772548540162049.jpg',
    original_avatar: null,
    banner: null,
    bio: 'وكالة إيران بالعربية للأنباء | صوت الجمهورية الاسلامية الايرانية في العالم العربي | مجازة رسمياً من وزارة الثقافة والإرشاد الإسلامي',
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 142,
    follower_count: 709612,
    ai_summary: null,
    political_category: {
      label: 'barandaz',
      prob: {
        eslahtalab: 0.21,
        edalatkhah: 0.11,
        ahmadinezhad: 0.07,
        restart: 0.03,
        osoolgera: 0.07,
        saltanat: 0.08,
        monafegh: 0.13,
        barandaz: 0.3,
      },
      support_state: 'opposite',
    },
    gender: {
      label: 'female',
      prob: {
        female: 0,
        male: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
  {
    id: '1212783083272855552',
    user_title: 'انجمن دوستی مردم ایران و اسراییل (درون ایران)',
    user_name: 'fcpii_official',
    avatar: 'https://s3.synappse.ir/twitter/profiles/1212783083272855552.jpg',
    original_avatar: '',
    banner: '',
    bio: 'Monitoring Real-Time News & Open Source Intelligence • Middle East • US • Global Events • Anything on my mind • https://t.co/EdfPtw4fiP • DM for Business Inquiries',
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 628,
    follower_count: 420568,
    ai_summary: null,
    political_category: {
      label: 'saltanat',
      prob: {
        eslahtalab: 0.02,
        saltanat: 0.41,
        barandaz: 0.39,
        monafegh: 0.01,
        edalatkhah: 0,
        osoolgera: 0,
        ahmadinezhad: 0.01,
        restart: 0.15,
      },
      support_state: 'opposite',
    },
    gender: {
      label: 'female',
      prob: {
        male: 0,
        female: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
  {
    id: '1041674345926389760',
    user_title: 'ایران اینترنشنال ورزشی',
    user_name: 'iranintlsport',
    avatar: 'https://s3.synappse.ir/twitter/profiles/1041674345926389760.jpg',
    original_avatar:
      'https://pbs.twimg.com/profile_images/1380466309432025088/MU2chgnU_normal.jpg',
    banner:
      'https://pbs.twimg.com/profile_banners/1041674345926389760/**********',
    bio: 'ایران اینترنشنال ورزشی',
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 10,
    follower_count: 304024,
    ai_summary: null,
    political_category: {
      label: 'eslahtalab',
      prob: {
        eslahtalab: 0.52,
        edalatkhah: 0.01,
        ahmadinezhad: 0.01,
        osoolgera: 0.01,
        saltanat: 0.04,
        monafegh: 0.01,
        barandaz: 0.38,
        restart: 0.01,
      },
      support_state: 'supporter',
    },
    gender: {
      label: 'female',
      prob: {
        female: 0,
        male: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
  {
    id: '789939670016724992',
    user_title: 'سکسی ایرانی',
    user_name: 'sexy_irani_',
    avatar: 'https://s3.synappse.ir/twitter/profiles/789939670016724992.jpg',
    original_avatar: null,
    banner: null,
    bio: 'عکس ها و فیلم های سکسی و سوپر خفن ایرانی از دختر های هلو و حشری ایرانی.\nاین اکانت با فی خوب واگذار میشود',
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 25,
    follower_count: 231756,
    ai_summary: null,
    political_category: {
      label: 'unknown',
      prob: {
        eslahtalab: 0,
        edalatkhah: 0,
        ahmadinezhad: 0,
        restart: 0,
        osoolgera: 0,
        saltanat: 0,
        monafegh: 0,
        barandaz: 0,
      },
      support_state: null,
    },
    gender: {
      label: 'female',
      prob: {
        female: 0,
        male: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
  {
    id: '1263614720877133825',
    user_title: 'فلات ایران',
    user_name: 'IranianPlateau',
    avatar: 'https://s3.synappse.ir/twitter/profiles/1263614720877133825.jpg',
    original_avatar:
      'https://pbs.twimg.com/profile_images/1801689544820932608/d5fBn4GG_normal.jpg',
    banner:
      'https://pbs.twimg.com/profile_banners/1263614720877133825/**********',
    bio: 'پاینده ایران.',
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 15,
    follower_count: 168785,
    ai_summary: null,
    political_category: {
      label: 'monafegh',
      prob: {
        eslahtalab: 0.02,
        edalatkhah: 0.01,
        ahmadinezhad: 0,
        osoolgera: 0,
        saltanat: 0.17,
        monafegh: 0.71,
        barandaz: 0.08,
        restart: 0,
      },
      support_state: 'opposite',
    },
    gender: {
      label: 'female',
      prob: {
        female: 0,
        male: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
  {
    id: '742451244028039168',
    user_title: 'روزنامه ایران',
    user_name: 'IranNewspaper',
    avatar: 'https://s3.synappse.ir/twitter/profiles/742451244028039168.jpg',
    original_avatar:
      'https://pbs.twimg.com/profile_images/1898686014266175488/VLgK9xOz_normal.jpg',
    banner:
      'https://pbs.twimg.com/profile_banners/742451244028039168/**********',
    bio: 'حساب کاربری رسمی روزنامه ایران',
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 12,
    follower_count: 159088,
    ai_summary: null,
    political_category: {
      label: 'eslahtalab',
      prob: {
        eslahtalab: 0.37,
        edalatkhah: 0.24,
        ahmadinezhad: 0.03,
        osoolgera: 0.33,
        saltanat: 0.01,
        monafegh: 0,
        barandaz: 0.01,
        restart: 0.01,
      },
      support_state: 'supporter',
    },
    gender: {
      label: 'female',
      prob: {
        female: 0,
        male: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
  {
    id: '**********',
    user_title: 'ایران وایر',
    user_name: 'iranwire',
    avatar: 'https://s3.synappse.ir/twitter/profiles/**********.jpg',
    original_avatar:
      'https://pbs.twimg.com/profile_images/1506334088000548864/hMcVWYaD_normal.jpg',
    banner: 'https://pbs.twimg.com/profile_banners/**********/**********',
    bio: 'گزارش‌های خبری، با کمک شهروند خبرنگاران ایرانی\nعكس، خبر و فيلم هاى خود را براى ما بفرستيد: https://t.co/icwBripkj9\nکانال واتساپ: https://t.co/wRGjp8AwJ1',
    website: null,
    location: null,
    ai_location: null,
    birthday: null,
    join_date: null,
    tweet_count: null,
    following_count: 148,
    follower_count: 132571,
    ai_summary:
      'アイラン وایر (IranWire) یک حساب توئیتر است که به انتشار گزارش‌های خبری حرفه‌ای و متفاوت از ایران می‌پردازد. این حساب توئیتر که توسط شهروند خبرنگاران ایرانی اداره می‌شود، اطلاعات و اخبار 하루های مختلف مانند حوادث کار، ورزش، سیاست، امنیت دفاع و زندگی روزمره را به اشتراک می‌گذارد. meisten از پست‌های این حساب توئیتر شامل خبرهای اصلی و مهم کشور ایران است و گاهی اوقات با استفاده از تصاویر و فیلم‌ها، همراه با لینک‌های مربوط به داستان‌های کامل، ارائه می‌شوند. از آنجایی که این حساب توئیتر به انتشار اخبار و گزارش‌های معتبر می‌پردازد، مخاطبان آن علاقه‌مندان به خبرهای ایران و مسائل مرتبط با آن هستند.',
    political_category: {
      label: 'barandaz',
      prob: {
        eslahtalab: 0.04,
        edalatkhah: 0,
        ahmadinezhad: 0.01,
        osoolgera: 0,
        saltanat: 0.05,
        monafegh: 0.02,
        barandaz: 0.87,
        restart: 0,
      },
      support_state: 'opposite',
    },
    gender: {
      label: 'unknown',
      prob: {
        female: 0,
        male: 0,
      },
    },
    age: {
      range: 'unknown',
      label: 'unknown',
    },
    similar_accounts: null,
  },
];

export default function CreateDashboard() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [form1, setForm1] = useState({ title: '', description: '' });
  const [form2, setForm2] = useState<Form2>({
    params: { runtime: { gap: 24 * 60 * 60 * 1000 }, query: { q: '' } }, // 24 hours in milliseconds
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [createdDashboard, setCreatedDashboard] = useState<Dashboard | null>(
    null
  );
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const lastGapValueRef = useRef<number>(12 * 60 * 60 * 1000);

  const steps = [
    {
      title: 'عنوان داشبورد',
    },
    {
      title: 'تنظیمات فیلتر',
    },
    {
      title: 'تایید نهایی',
    },
  ];

  const breadcrumbItems = [
    { label: 'داشبورد', href: '/dashboard' },
    { label: 'ایجاد داشبورد جدید' },
  ];

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      if (name === 'title' || name === 'description') {
        setForm1((prev) => ({ ...prev, [name]: value }));
        setErrors((prev) => ({ ...prev, [name]: '' }));
      } else if (name === 'searchQuery') {
        setForm2((prev) => ({
          ...prev,
          params: {
            ...prev.params,
            query: {
              ...prev.params.query,
              q: value,
            },
          },
        }));
        setErrors((prev) => ({ ...prev, [name]: '' }));
      }
    },
    []
  );

  const handleHashtagsChange = useCallback((newTags: string[]) => {
    setForm2((prev) => ({
      ...prev,
      params: {
        ...prev.params,
        query: {
          ...prev.params.query,
          hashtags: newTags,
        },
      },
    }));
  }, []);

  const handleSourcesChange = useCallback((newSources: string[]) => {
    setSelectedSources(newSources);
    setForm2((prev) => ({
      ...prev,
      params: {
        ...prev.params,
        query: {
          ...prev.params.query,
          sources: newSources,
        },
      },
    }));
  }, []);

  const handleTimePeriodChange = useCallback((gap: number) => {
    console.log('TimePeriodPicker onChange called with:', gap, 'milliseconds');
    console.log('Last gap value from ref:', lastGapValueRef.current);

    // Add a small tolerance for floating point comparison
    const tolerance = 1000; // 1 second tolerance

    // Only update if the value is actually different (with tolerance) to prevent infinite loops
    if (Math.abs(lastGapValueRef.current - gap) > tolerance) {
      console.log('Updating gap from', lastGapValueRef.current, 'to', gap);
      lastGapValueRef.current = gap;

      setForm2((prev) => ({
        ...prev,
        params: {
          ...prev.params,
          runtime: {
            ...prev.params.runtime,
            gap,
          },
        },
      }));
    } else {
      console.log('Gap value unchanged (within tolerance), skipping update');
    }
  }, []);

  const form1Schema = z.object({
    title: z
      .string()
      .min(3, { message: 'عنوان باید حداقل ۳ کاراکتر داشته باشد.' }),
    description: z.string().optional(),
  });

  const form2Schema = z.object({
    params: z.object({
      runtime: z.object({
        gap: z.number().positive({ message: 'بازه زمانی باید مثبت باشد.' }),
      }),
      query: z.object({
        q: z.string().min(2, { message: 'عبارت جستجو الزامی است.' }),
        report_type: z.string().optional(),
        hashtags: z.array(z.string()).optional(),
        sources: z.array(z.string()).optional(),
      }),
    }),
  });

  const handleCreateDashboard = async () => {
    setIsSubmitting(true);
    setErrors({});

    try {
      // Generate a preview based on the form data
      // const preview = `داشبورد ${form1.title} - جستجو: ${form2.params.query.q}${
      //   form2.params.query.hashtags && form2.params.query.hashtags.length > 0
      //     ? ` - هشتگ‌ها: ${form2.params.query.hashtags.join(', ')}`
      //     : ''
      // }`;

      // Combine form1 and form2 data into the payload
      const payload: CreateDashboardPayload = {
        title: form1.title,
        description: form1.description.trim() || undefined,
        params: form2.params,
      };

      // Create the dashboard
      const dashboard = await createDashboard(payload);
      setCreatedDashboard(dashboard);
      setCurrentStep(2);
    } catch (error) {
      console.error('Dashboard creation error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'خطا در ایجاد داشبورد';

      // Handle Zod validation errors by mapping them to form fields
      if (error instanceof z.ZodError) {
        const errorMap: Record<string, string> = {};
        error.issues.forEach((issue) => {
          const path = issue.path.join('.');
          if (path.includes('title')) {
            errorMap['title'] = issue.message;
          } else if (path.includes('description')) {
            errorMap['description'] = issue.message;
          } else if (path.includes('q')) {
            errorMap['searchQuery'] = issue.message;
          } else {
            errorMap['general'] = issue.message;
          }
        });
        setErrors(errorMap);
      } else {
        setErrors({ general: errorMessage });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = () => {
    if (currentStep === 0) {
      const result = form1Schema.safeParse(form1);

      if (!result.success) {
        const errorMap: Record<string, string> = {};
        result.error.issues.forEach((issue) => {
          const field = String(issue.path[0]);
          errorMap[field] = issue.message;
        });
        setErrors(errorMap);
        return;
      }

      setCurrentStep(1);
    } else if (currentStep === 1) {
      const result = form2Schema.safeParse(form2);

      if (!result.success) {
        const errorMap: Record<string, string> = {};
        result.error.issues.forEach((issue) => {
          const path = issue.path.join('.');
          if (path.includes('q')) {
            errorMap['searchQuery'] = issue.message;
          } else {
            errorMap[String(issue.path[0])] = issue.message;
          }
        });
        setErrors(errorMap);
        return;
      }

      // Instead of going to step 2, trigger dashboard creation
      handleCreateDashboard();
    }
  };

  const handleBack = useCallback(() => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  }, []);

  const handleCancel = useCallback(() => {
    window.history.back();
  }, []);

  const handleNavigateToCreate = useCallback(() => {
    navigate('/dashboard/create');
  }, [navigate]);

  const handleNavigateToDashboard = useCallback(() => {
    navigate('/dashboard');
  }, [navigate]);

  const handleNavigateToCreateReport = useCallback(() => {
    if (createdDashboard) {
      navigate(`/dashboard/${createdDashboard.id}/create-report`);
    }
  }, [navigate, createdDashboard]);

  // Memoize the time period value to prevent unnecessary re-renders
  const timePeriodValue = useMemo(() => {
    // Update the ref when the value changes from other sources
    lastGapValueRef.current = form2.params.runtime.gap;
    return form2.params.runtime.gap;
  }, [form2.params.runtime.gap]);

  return (
    <div className="min-h-full p-8">
      <Breadcrumb items={breadcrumbItems} />
      <div className="mx-auto mt-8 flex w-full max-w-7xl flex-col items-center">
        <h1 className="text-4xl font-bold text-white">ایجاد داشبورد جدید</h1>
        <div className="my-4 h-[2px] w-1/12 bg-gray-300"></div>

        <Stepper steps={steps} currentStep={currentStep} className="mt-8" />

        <div className="relative mt-8 w-full pb-8">
          <AnimatePresence mode="wait">
            {currentStep === 0 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="mx-auto w-full max-w-2xl space-y-6"
              >
                <TextInput
                  label="عنوان داشبورد"
                  name="title"
                  value={form1.title}
                  onChange={handleChange}
                  placeholder="عنوان داشبورد را وارد کنید"
                  error={errors.title}
                />
                <TextArea
                  label="توضیحات"
                  name="description"
                  value={form1.description}
                  onChange={handleChange}
                  placeholder="متن مورد نظر خود را بنویسید"
                  error={errors.description}
                />
                <div className="flex flex-col gap-2 sm:flex-row">
                  <div className="flex-grow"></div>
                  <Button variant="secondary" onClick={handleCancel}>
                    انصراف
                  </Button>
                  <Button
                    onClick={handleNext}
                    icon={<FileText className="h-4 w-4" />}
                    iconPosition="start"
                  >
                    ثبت و ادامه
                  </Button>
                </div>
              </motion.div>
            )}

            {currentStep === 1 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="mx-auto w-full max-w-4xl space-y-6"
              >
                <SearchDrawerInput
                  label="عبارت جستجو"
                  name="searchQuery"
                  placeholder="عبارت جستجو خود را وارد کنید"
                  value={form2.params.query.q}
                  onChange={handleChange}
                  error={errors.searchQuery}
                />

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <TagInput
                    label="هشتگ‌ها"
                    mode="scroll"
                    name="hashtags"
                    value={form2.params.query.hashtags}
                    onChange={handleHashtagsChange}
                    placeholder="هشتگ‌های مورد نظر را وارد کنید"
                  />

                  <SourceInput
                    label="انتخاب منابع"
                    value={selectedSources}
                    onChange={handleSourcesChange}
                    placeholder="منابع مورد نظر خود را انتخاب کنید"
                    data={sampleSources}
                    quickSelectCount={5}
                  />
                </div>

                <TimePeriodPicker
                  value={timePeriodValue}
                  onChange={handleTimePeriodChange}
                  label="بازه زمانی نتایج گزارش"
                />

                <div className="h-[1px] bg-neutral-700"></div>

                {errors.general && (
                  <div className="rounded-md bg-red-50 p-4">
                    <div className="text-sm text-red-700">{errors.general}</div>
                  </div>
                )}

                <div className="flex flex-col gap-2 sm:flex-row">
                  <Button
                    variant="secondary"
                    onClick={handleBack}
                    disabled={isSubmitting}
                  >
                    قبلی
                  </Button>
                  <div className="flex-grow"></div>
                  <Button
                    variant="secondary"
                    onClick={handleCancel}
                    disabled={isSubmitting}
                  >
                    انصراف
                  </Button>
                  <Button
                    onClick={handleNext}
                    icon={<FileText className="h-4 w-4" />}
                    iconPosition="start"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'در حال ایجاد...' : 'ایجاد داشبورد'}
                  </Button>
                </div>
              </motion.div>
            )}

            {currentStep === 2 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="mx-auto w-full max-w-4xl space-y-6"
              >
                <div className="flex flex-col items-center">
                  <div className="bg-primary-500 flex h-16 w-16 items-center justify-center rounded-full">
                    <CheckIcon size={40} color="#FFFFFF" />
                  </div>

                  <div className="mt-8 text-[25px] font-bold text-white">
                    داشبورد گزارشات با موفقیت ایجاد شد
                  </div>

                  <div className="mt-6 text-xl font-medium text-stone-300">
                    {createdDashboard ? (
                      <>
                        داشبورد شما با عنوان "{createdDashboard.title}" و عبارت
                        جستجوی "{form2.params.query.q}" ایجاد شد.
                        {form2.params.query.hashtags &&
                          form2.params.query.hashtags.length > 0 && (
                            <div className="mt-2 text-lg">
                              هشتگ‌ها: {form2.params.query.hashtags.join(', ')}
                            </div>
                          )}
                        {form2.params.query.sources &&
                          form2.params.query.sources.length > 0 && (
                            <div className="mt-2 text-lg">
                              منابع: {form2.params.query.sources.join(', ')}
                            </div>
                          )}
                        {form1.description && (
                          <div className="mt-2 text-lg">
                            توضیحات: {form1.description}
                          </div>
                        )}
                      </>
                    ) : (
                      'داشبورد شما با موفقیت ایجاد شد.'
                    )}
                  </div>

                  <div className="mt-10 flex flex-row gap-4">
                    <Button
                      variant="secondary"
                      onClick={handleNavigateToCreate}
                    >
                      ایجاد داشبورد جدید
                    </Button>
                    <Button
                      variant="primary"
                      onClick={handleNavigateToDashboard}
                    >
                      بازگشت به داشبورد
                    </Button>
                    {createdDashboard && (
                      <Button
                        variant="primary"
                        onClick={handleNavigateToCreateReport}
                      >
                        ایجاد گزارش
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
