import { RouterProvider } from 'react-router-dom';
import { AuthProvider } from '@/context/AuthContext';
import { router } from './routes';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import SkipAuthIndicator from '@/components/common/SkipAuthIndicator';

function App() {
  return (
    <>
      <SkipAuthIndicator />
      <ErrorBoundary>
        <AuthProvider>
          <RouterProvider router={router} />
        </AuthProvider>
      </ErrorBoundary>
    </>
  );
}

export default App;
