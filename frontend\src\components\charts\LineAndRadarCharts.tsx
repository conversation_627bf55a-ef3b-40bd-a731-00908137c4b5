import React from 'react';
import Responsive<PERSON>hartWrapper from './ResponsiveChartWrapper';
import {
  createChartConfig,
  AXIS_CONFIG,
  RESPONSIVE_RULES,
} from './chartConfig';
import Highcharts from 'highcharts';
import HighchartsMore from 'highcharts/highcharts-more';

HighchartsMore(Highcharts);

// Types for line and radar charts
export interface LineChartSeries {
  name: string;
  data: number[] | Array<[number, number]>;
  type?: string;
}

export interface LineChartProps {
  series?: Array[];
  categories?: string[];
  title?: string;
  className?: string;
  showLegend?: boolean;
  xAxisTitle?: string;
  yAxisTitle?: string;
}

export interface RadarChartProps {
  data?: Array<{
    name: string;
    data: number[];
    pointPlacement?: string;
  }>;
  categories?: string[];
  title?: string;
  className?: string;
  showLegend?: boolean;
}

/**
 * نمودار خطی
 */
export const LineChart: React.FC<LineChartProps> = ({
  categories = [],
  title = 'نمودار خطی',
  className = '',
  showLegend = true,
  xAxisTitle = 'ماه',
  yAxisTitle = 'مقدار',
  series = [],
}) => {
  const options = createChartConfig(
    {
      chart: {
        type: 'line',
      },
      title: {
        text: title,
      },
      xAxis: {
        ...AXIS_CONFIG.xAxis,
        categories: categories,
        title: {
          text: xAxisTitle,
          style: AXIS_CONFIG.xAxis.title?.style,
        },
      },
      yAxis: {
        ...AXIS_CONFIG.yAxis,
        title: {
          text: yAxisTitle,
          style: AXIS_CONFIG.yAxis.title?.style,
        },
      },
      plotOptions: {
        line: {
          dataLabels: {
            enabled: false,
          },
          enableMouseTracking: true,
          marker: {
            enabled: true,
            radius: 4,
          },
        },
      },
      series: series.map((items) => ({
        ...items,
        type: 'line',
      })),
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

/**
 * نمودار راداری (Spider Chart)
 */
export const RadarChart: React.FC<RadarChartProps> = ({
  data = [
    { name: 'مهارت‌های فنی', data: [8, 7, 6, 9, 8], pointPlacement: 'on' },
    { name: 'مهارت‌های نرم', data: [6, 8, 7, 8, 6], pointPlacement: 'on' },
  ],
  categories = ['برنامه‌نویسی', 'طراحی', 'تحلیل', 'مدیریت', 'ارتباطات'],
  title = 'نمودار راداری',
  className = '',
  showLegend = true,
}) => {
  const options = createChartConfig(
    {
      chart: {
        polar: true,
        type: 'line',
      },
      title: {
        text: title,
      },
      pane: {
        size: '80%',
      },
      xAxis: {
        categories: categories,
        tickmarkPlacement: 'on',
        lineWidth: 0,
        gridLineColor: '#374151',
        labels: {
          style: {
            color: '#ffffff',
            fontFamily: 'IranYekanX',
            fontSize: '11px',
          },
        },
      },
      yAxis: {
        gridLineInterpolation: 'polygon',
        lineWidth: 0,
        min: 0,
        max: 10,
        gridLineColor: '#374151',
        labels: {
          style: {
            color: '#ffffff',
            fontFamily: 'IranYekanX',
            fontSize: '10px',
          },
        },
      },
      plotOptions: {
        series: {
          pointPlacement: 'on',
          marker: {
            enabled: true,
            radius: 4,
          },
        },
      },
      series: data.map((series) => ({
        ...series,
        type: 'line',
      })),
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

/**
 * نمودار چندضلعی (Polygon Chart)
 */
export const PolygonChart: React.FC<RadarChartProps> = ({
  data = [
    { name: 'منطقه A', data: [43000, 19000, 60000, 35000, 17000, 10000] },
    { name: 'منطقه B', data: [50000, 39000, 42000, 31000, 26000, 14000] },
  ],
  categories = ['فروش', 'مارکتینگ', 'توسعه', 'خدمات', 'IT', 'اداری'],
  title = 'نمودار چندضلعی',
  className = '',
  showLegend = true,
}) => {
  const options = createChartConfig(
    {
      chart: {
        polar: true,
        type: 'area',
      },
      title: {
        text: title,
      },
      pane: {
        size: '80%',
      },
      xAxis: {
        categories: categories,
        tickmarkPlacement: 'on',
        lineWidth: 0,
        gridLineColor: '#374151',
        labels: {
          style: {
            color: '#ffffff',
            fontFamily: 'IranYekanX',
            fontSize: '11px',
          },
        },
      },
      yAxis: {
        gridLineInterpolation: 'polygon',
        lineWidth: 0,
        min: 0,
        gridLineColor: '#374151',
        labels: {
          style: {
            color: '#ffffff',
            fontFamily: 'IranYekanX',
            fontSize: '10px',
          },
        },
      },
      plotOptions: {
        area: {
          fillOpacity: 0.3,
          marker: {
            enabled: true,
            radius: 3,
          },
        },
      },
      series: data.map((series) => ({
        ...series,
        type: 'area',
      })),
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

/**
 * نمودار گلباد (Wind Rose)
 */
export const WindRoseChart: React.FC<{
  data?: Array<{
    name: string;
    data: Array<[number, number]>;
  }>;
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = ({
  data = [
    {
      name: 'باد ضعیف',
      data: [
        [0, 1.5],
        [45, 2.1],
        [90, 1.8],
        [135, 1.2],
        [180, 0.9],
        [225, 1.4],
        [270, 2.3],
        [315, 1.7],
      ],
    },
    {
      name: 'باد متوسط',
      data: [
        [0, 2.5],
        [45, 3.1],
        [90, 2.8],
        [135, 2.2],
        [180, 1.9],
        [225, 2.4],
        [270, 3.3],
        [315, 2.7],
      ],
    },
  ],
  title = 'نمودار گلباد',
  className = '',
  showLegend = true,
}) => {
  const options = createChartConfig(
    {
      chart: {
        polar: true,
        type: 'column',
      },
      title: {
        text: title,
      },
      pane: {
        size: '85%',
        startAngle: 0,
      },
      xAxis: {
        min: 0,
        max: 360,
        tickInterval: 45,
        labels: {
          formatter: function () {
            const directions = [
              'شمال',
              'شمال‌شرق',
              'شرق',
              'جنوب‌شرق',
              'جنوب',
              'جنوب‌غرب',
              'غرب',
              'شمال‌غرب',
            ];
            return directions[this.value / 45] || this.value + '°';
          },
          style: {
            color: '#ffffff',
            fontFamily: 'IranYekanX',
            fontSize: '10px',
          },
        },
      },
      yAxis: {
        min: 0,
        gridLineColor: '#374151',
        labels: {
          style: {
            color: '#ffffff',
            fontFamily: 'IranYekanX',
            fontSize: '10px',
          },
        },
      },
      plotOptions: {
        column: {
          pointPadding: 0,
          groupPadding: 0,
          borderWidth: 0,
        },
      },
      series: data.map((series) => ({
        ...series,
        type: 'column',
      })),
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};

export interface PolarRadialBarChartProps {
  data?: Array<{
    name: string;
    data: number[];
  }>;
  categories?: string[];
  title?: string;
  className?: string;
  showLegend?: boolean;
}

/**
 * نمودار میله‌ای شعاعی قطبی (Polar Radial Bar Chart)
 */
export const PolarRadialBarChart: React.FC<PolarRadialBarChartProps> = ({
  data = [],
  categories = [],
  title = 'نمودار میله‌ای شعاعی قطبی',
  className = '',
  showLegend = true,
}) => {
  const options = createChartConfig(
    {
      chart: {
        type: 'column',
        polar: true,
        inverted: true,
      },
      title: {
        text: title,
      },
      pane: {
        size: '85%',
        innerSize: '20%',
        endAngle: 270,
      },
      xAxis: {
        categories: categories,
        tickmarkPlacement: 'on',
        lineWidth: 0,
        gridLineColor: '#374151',
        labels: {
          style: {
            color: '#ffffff',
            fontFamily: 'IranYekanX',
            fontSize: '11px',
          },
        },
      },
      yAxis: {
        lineWidth: 0,
        min: 0,
        gridLineColor: '#374151',
        labels: {
          style: {
            color: '#ffffff',
            fontFamily: 'IranYekanX',
            fontSize: '10px',
          },
        },
      },
      plotOptions: {
        column: {
          pointPadding: 0,
          groupPadding: 0.1,
          borderWidth: 0,
          dataLabels: {
            enabled: false,
          },
        },
      },
      series: data.map((series) => ({
        ...series,
        type: 'column',
      })),
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={200}
    />
  );
};
