@import 'tailwindcss';

/* CSS Reset for better control */
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

:root {
  --background-color: #000000;
}

@theme {
  --color-primary-400: #5bf7fa;
  --color-primary-500: #05a0a3;
  --font-iran-yekan-x: 'IranYekanX';
  --font-iran-yekan-x-fanum: 'IranYekanXFaNum';
}

@layer utilities {
  .scrollbar-thin {
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
      border-radius: 50px;
    }
    &::-webkit-scrollbar-track {
      background: #d3d3d3; /* lightgray */
      border-radius: 50px;
    }
    &::-webkit-scrollbar-thumb {
      background: #a9a9a9; /* darkgray */
      border-radius: 50px;
    }
    &::-webkit-scrollbar-thumb:hover {
      background: #8c8c8c; /* slightly darker gray on hover */
    }
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
    &::-webkit-scrollbar {
      display: none;
    }
  }

  .bg-dotted {
    background-image: radial-gradient(
      rgba(217, 217, 217, 0.05) 4px,
      transparent 0
    );
    background-size: 25px 25px;
  }
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  background-color: var(--background-color);
  font-family: var(--font-iran-yekan-x), serif;
  overflow-x: hidden;
}

#root {
  height: 100%;
  margin: 0;
  padding: 0;
}

.input-no-spinner::-webkit-inner-spin-button,
.input-no-spinner::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.input-no-spinner {
  -moz-appearance: textfield;
}

svg {
  font-family: var(--font-iran-yekan-x), serif !important;
}
