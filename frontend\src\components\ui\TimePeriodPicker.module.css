.toggle {
  direction: rtl;
}

.slider {
  height: 5px;
  width: 100%;
  cursor: pointer;
  appearance: none;
  border-radius: 0.5rem;
}

/* Chrome / Safari / Edge */
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #5bf7fa;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

/* Firefox */
.slider::-moz-range-thumb {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #5bf7fa;
  cursor: pointer;
}
