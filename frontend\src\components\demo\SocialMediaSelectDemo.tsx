import React, { useState } from 'react';
import SocialMediaSelect from '@/components/ui/SocialMediaSelect';
import { socialItems } from '@/constants/socialMedia';

const SocialMediaSelectDemo: React.FC = () => {
  const [selectedSocials, setSelectedSocials] = useState<number[]>([1]);
  const [error, setError] = useState<string>('');

  const handleSelectionChange = (selectedIds: number[]) => {
    setSelectedSocials(selectedIds);
    setError(''); // Clear error when selection changes
  };

  const handleValidate = () => {
    if (selectedSocials.length === 0) {
      setError('حداقل یک بستر باید انتخاب شود.');
    } else {
      setError('');
      alert(
        `انتخاب شده: ${selectedSocials
          .map((id) => socialItems.find((item) => item.id === id)?.title)
          .join(', ')}`
      );
    }
  };

  return (
    <div className="min-h-screen bg-neutral-900 p-8">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-center text-3xl font-bold text-white">
          نمایش کامپوننت انتخاب شبکه‌های اجتماعی
        </h1>

        <SocialMediaSelect
          label="بستر‌های مورد نظر خود را انتخاب کنید"
          value={selectedSocials}
          onChange={handleSelectionChange}
          error={error}
          className="mb-6"
        />

        <div className="text-center">
          <button
            onClick={handleValidate}
            className="bg-primary-500 hover:bg-primary-600 rounded-lg px-6 py-3 text-white transition-colors"
          >
            تایید انتخاب
          </button>
        </div>

        <div className="mt-6 rounded-lg bg-neutral-800 p-4">
          <h3 className="mb-2 text-lg font-medium text-white">انتخاب فعلی:</h3>
          <p className="text-neutral-300">
            {selectedSocials.length > 0
              ? selectedSocials
                  .map(
                    (id) => socialItems.find((item) => item.id === id)?.title
                  )
                  .join(', ')
              : 'هیچ بستری انتخاب نشده'}
          </p>
          <p className="mt-2 text-sm text-neutral-400">
            تعداد انتخاب شده: {selectedSocials.length}
          </p>
        </div>
      </div>
    </div>
  );
};

export default SocialMediaSelectDemo;
