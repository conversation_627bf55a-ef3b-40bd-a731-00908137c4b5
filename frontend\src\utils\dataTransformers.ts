import { ChartType } from '@/constants/chartTypes';
import {
  ReportData,
  ProcessData,
  AdvanceData,
  PostsData,
  CloudData,
  SourceInfoData,
  TopSourcesData,
  ContentTypeDistributionData,
  SentimentsData,
  CategoriesData,
  StatisticalData,
  EmotionData,
  TrendsData,
  InfluenceGraphData,
  ImpactGraphData,
  OpinionGraphData,
} from '@/types/dashboard';

// Chart data interfaces
export interface ChartSeries {
  name: string;
  data: number[] | Array<[number, number]>;
  type?: string;
}

export interface ChartData {
  series?: ChartSeries[];
  categories?: string[];
  data?: any[];
  nodes?: any[];
  links?: any[];
  value?: number;
  title?: string;
}

// Data transformer interface
export interface DataTransformer {
  transform(data: any, chartType: ChartType, widget: any): ChartData;
}

// Base transformer class
abstract class BaseTransformer implements DataTransformer {
  abstract transform(data: any, chartType: ChartType, widget: any): ChartData;

  protected formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fa-IR', {
      month: 'short',
      day: 'numeric',
    });
  }

  protected formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toLocaleDateString('fa-IR', {
      month: 'short',
      day: 'numeric',
    });
  }
}

// Process data transformer
class ProcessTransformer extends BaseTransformer {
  transform(
    data: { twitter: ProcessData[] } | { [source: string]: ProcessData[] },
    chartType: ChartType,
    widget: any
  ): ChartData {
    if ('twitter' in data) {
      // Single source data
      const processedData = data.twitter.map((item) => ({
        x: new Date(item.timestamp)
          .toLocaleDateString('fa-IR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            // hour: '2-digit',
            // minute: '2-digit',
          })
          .replace(/[\u200E]/g, ''),
        y: item.count,
        name: this.formatDate(item.datetime),
      }));

      switch (chartType) {
        case 'line':
          return {
            series: [
              {
                name: 'تعداد',
                data: processedData.map((item) => [item.x, item.y]),
              },
            ],
            categories: processedData.map((item) => item.name),
          };
        case 'bar':
          return {
            series: [
              {
                name: 'تعداد',
                data: processedData.map((item) => item.y),
              },
            ],
            categories: processedData.map((item) => item.name),
          };
        case 'table':
          return {
            data: {
              headers: ['زمان', 'تعداد'],
              rows: processedData.map((item) => [item.x, item.y]),
            },
            maxHeigth: widget?.params?.position?.height
              ? 50 * widget?.params?.position?.height + 'px'
              : '400px',
          };
        case 'badge':
          const total = processedData.reduce((sum, item) => sum + item.y, 0);
          return { value: total };
        default:
          return { data: processedData };
      }
    } else {
      // Multiple sources data
      const sources = Object.keys(data);
      const series: ChartSeries[] = sources.map((source) => ({
        name: source,
        data: data[source].map((item) => item.count),
      }));

      const categories =
        data[sources[0]]?.map((item) => this.formatDate(item.datetime)) || [];

      return { series, categories };
    }
  }
}

// Cloud data transformer
class CloudTransformer extends BaseTransformer {
  transform(data: CloudData[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'cloud':
        return {
          data: data.map((item) => ({
            name: item.text,
            weight: item.weight,
          })),
        };
      case 'bar':
        return {
          series: [
            {
              name: 'وزن',
              data: data.map((item) => item.weight),
            },
          ],
          categories: data.map((item) => item.text),
        };
      default:
        return { data };
    }
  }
}

// Source info transformer
class SourceInfoTransformer extends BaseTransformer {
  transform(data: SourceInfoData[], chartType: ChartType): ChartData {
    console.log('tosldfa');
    console.log(data);
    console.log(chartType);
    switch (chartType) {
      case 'pie':
      case 'donut':
        return {
          series: [
            {
              name: 'منابع',
              data: data.map((item) => ({
                name: item.source,
                y: item.count,
              })),
            },
          ],
        };
      case 'bar':
        return {
          series: [
            {
              name: 'تعداد',
              data: data.map((item) => item.count),
            },
          ],
          categories: data.map((item) => item.source),
        };
      case 'radial':
        return {
          data: [
            {
              name: 'تعداد',
              data: data.twitter.map((item) => item.count),
            },
          ],
          categories: data.twitter.map((item) => item.title),
        };
      case 'table':
        return {
          data: {
            headers: ['منبع', 'تعداد'],
            rows: data.twitter.map((item) => [item.title, item.count]),
          },
        };
      default:
        return { data };
    }
  }
}

// Sentiments transformer
class SentimentsTransformer extends BaseTransformer {
  transform(data: SentimentsData[], chartType: ChartType): ChartData {
    console.log('data');
    console.log(data);
    const sentimentLabels = {
      positive: 'مثبت',
      negative: 'منفی',
      neutral: 'خنثی',
    };

    switch (chartType) {
      case 'pie':
      case 'donut':
        return {
          series: [
            {
              name: 'احساسات',
              data: data.twitter.map((item) => ({
                name: sentimentLabels[item.key],
                y: item.count,
              })),
            },
          ],
        };
      case 'bar':
        return {
          series: [
            {
              name: 'تعداد',
              data: data.map((item) => item.count),
            },
          ],
          categories: data.map((item) => sentimentLabels[item.sentiment]),
        };
      default:
        return { data };
    }
  }
}

// Categories transformer
class CategoriesTransformer extends BaseTransformer {
  transform(data: CategoriesData[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'pie':
      case 'donut':
        return {
          series: [
            {
              name: 'دسته‌بندی‌ها',
              data: data.map((item) => ({
                name: item.category,
                y: item.count,
              })),
            },
          ],
        };
      case 'bar':
        return {
          series: [
            {
              name: 'تعداد',
              data: data.map((item) => item.count),
            },
          ],
          categories: data.map((item) => item.category),
        };
      default:
        return { data };
    }
  }
}

// Statistical transformer
class StatisticalTransformer extends BaseTransformer {
  transform(data: StatisticalData[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'badge':
        return { value: data[0]?.value || 0 };
      case 'bar':
        return {
          series: [
            {
              name: 'مقدار',
              data: data.map((item) => item.value),
            },
          ],
          categories: data.map((item) => item.metric),
        };
      case 'table':
        return {
          data: data.map((item) => ({
            متریک: item.metric,
            مقدار: item.value,
            تغییر: item.change || 0,
            نوع_تغییر: item.changeType || 'stable',
          })),
        };
      default:
        return { data };
    }
  }
}

// Posts transformer
class PostsTransformer extends BaseTransformer {
  transform(data: PostsData[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'list':
        return {
          data: data.map((item) => ({
            title: item.content.substring(0, 100) + '...',
            author: item.author,
            date: this.formatDate(item.datetime),
            source: item.source,
            engagement: item.engagement || 0,
          })),
        };
      case 'table':
        return {
          data: data.map((item) => ({
            محتوا: item.content.substring(0, 50) + '...',
            نویسنده: item.author,
            تاریخ: this.formatDate(item.datetime),
            منبع: item.source,
          })),
        };
      default:
        return { data };
    }
  }
}

// Emotion transformer
class EmotionTransformer extends BaseTransformer {
  transform(data: EmotionData[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'pie':
      case 'donut':
        return {
          series: [
            {
              name: 'احساسات',
              data: data.map((item) => ({
                name: item.emotion,
                y: item.count,
              })),
            },
          ],
        };
      case 'bar':
        return {
          series: [
            {
              name: 'تعداد',
              data: data.map((item) => item.count),
            },
          ],
          categories: data.map((item) => item.emotion),
        };
      case 'radar':
        return {
          series: [
            {
              name: 'احساسات',
              data: data.map((item) => item.count),
            },
          ],
          categories: data.map((item) => item.emotion),
        };
      default:
        return { data };
    }
  }
}

// Trends transformer
class TrendsTransformer extends BaseTransformer {
  transform(data: TrendsData[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'line':
        return {
          series: [
            {
              name: 'روند',
              data: data.map((item) => [
                new Date(item.datetime).getTime(),
                item.value,
              ]),
            },
          ],
          categories: data.map((item) => this.formatDate(item.datetime)),
        };
      case 'bar':
        return {
          series: [
            {
              name: 'مقدار',
              data: data.map((item) => item.value),
            },
          ],
          categories: data.map((item) => this.formatDate(item.datetime)),
        };
      default:
        return { data };
    }
  }
}

// Graph transformers
class InfluenceGraphTransformer extends BaseTransformer {
  transform(data: InfluenceGraphData, chartType: ChartType): ChartData {
    switch (chartType) {
      case 'network_graph':
        return {
          nodes: data.nodes,
          links: data.links,
        };
      default:
        return { data };
    }
  }
}

class ImpactGraphTransformer extends BaseTransformer {
  transform(data: ImpactGraphData, chartType: ChartType): ChartData {
    switch (chartType) {
      case 'network_graph':
        return {
          nodes: data.nodes,
          links: data.links,
        };
      default:
        return { data };
    }
  }
}

class OpinionGraphTransformer extends BaseTransformer {
  transform(data: OpinionGraphData, chartType: ChartType): ChartData {
    switch (chartType) {
      case 'network_graph':
        return {
          nodes: data.nodes,
          links: data.links,
        };
      default:
        return { data };
    }
  }
}

// Content type distribution transformer
class ContentTypeDistributionTransformer extends BaseTransformer {
  transform(
    data: ContentTypeDistributionData[],
    chartType: ChartType
  ): ChartData {
    switch (chartType) {
      case 'pie':
      case 'donut':
        return {
          series: [
            {
              name: 'نوع محتوا',
              data: data.map((item) => ({
                name: item.type,
                y: item.count,
              })),
            },
          ],
        };
      case 'bar':
        return {
          series: [
            {
              name: 'تعداد',
              data: data.map((item) => item.count),
            },
          ],
          categories: data.map((item) => item.type),
        };
      default:
        return { data };
    }
  }
}

// Search suggest transformer
class SearchSuggestTransformer extends BaseTransformer {
  transform(data: any[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'cloud':
        return {
          data: data.map((item) => ({
            name: item.query || item.phrase || item.text,
            weight: item.relevance || item.count || item.weight,
          })),
        };
      case 'bar':
        return {
          series: [
            {
              name: 'امتیاز',
              data: data.map(
                (item) => item.relevance || item.count || item.weight
              ),
            },
          ],
          categories: data.map(
            (item) => item.query || item.phrase || item.text
          ),
        };
      case 'list':
        return {
          data: data.map((item) => ({
            title: item.query || item.phrase || item.text,
            score: item.relevance || item.count || item.weight,
            description: item.description || '',
          })),
        };
      default:
        return { data };
    }
  }
}

// Offensive content transformer
class OffensiveTransformer extends BaseTransformer {
  transform(data: any[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'list':
        return {
          data: data.map((item) => ({
            title: item.content?.substring(0, 100) + '...' || 'محتوا',
            severity: item.severity || 'low',
            date: item.datetime ? this.formatDate(item.datetime) : '',
            source: item.source || 'نامشخص',
          })),
        };
      case 'pie':
        const severityCounts = data.reduce((acc, item) => {
          const severity = item.severity || 'low';
          acc[severity] = (acc[severity] || 0) + 1;
          return acc;
        }, {});

        return {
          series: [
            {
              name: 'شدت',
              data: Object.entries(severityCounts).map(([severity, count]) => ({
                name:
                  severity === 'low'
                    ? 'کم'
                    : severity === 'medium'
                      ? 'متوسط'
                      : 'زیاد',
                y: count as number,
              })),
            },
          ],
        };
      case 'table':
        return {
          data: data.map((item) => ({
            محتوا: item.content?.substring(0, 50) + '...' || 'محتوا',
            شدت: item.severity || 'low',
            تاریخ: item.datetime ? this.formatDate(item.datetime) : '',
            منبع: item.source || 'نامشخص',
          })),
        };
      default:
        return { data };
    }
  }
}

// Advertise content transformer
class AdvertiseTransformer extends BaseTransformer {
  transform(data: any[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'list':
        return {
          data: data.map((item) => ({
            title: item.content?.substring(0, 100) + '...' || 'محتوا',
            brand: item.brand || 'نامشخص',
            date: item.datetime ? this.formatDate(item.datetime) : '',
            source: item.source || 'نامشخص',
            engagement: item.engagement || 0,
          })),
        };
      case 'bar':
        const brandCounts = data.reduce((acc, item) => {
          const brand = item.brand || 'نامشخص';
          acc[brand] = (acc[brand] || 0) + 1;
          return acc;
        }, {});

        return {
          series: [
            {
              name: 'تعداد تبلیغات',
              data: Object.values(brandCounts) as number[],
            },
          ],
          categories: Object.keys(brandCounts),
        };
      case 'table':
        return {
          data: data.map((item) => ({
            محتوا: item.content?.substring(0, 50) + '...' || 'محتوا',
            برند: item.brand || 'نامشخص',
            تاریخ: item.datetime ? this.formatDate(item.datetime) : '',
            منبع: item.source || 'نامشخص',
            تعامل: item.engagement || 0,
          })),
        };
      default:
        return { data };
    }
  }
}

// Related content transformer
class RelatedContentTransformer extends BaseTransformer {
  transform(data: any[], chartType: ChartType): ChartData {
    switch (chartType) {
      case 'list':
        return {
          data: data.map((item) => ({
            title: item.content?.substring(0, 100) + '...' || 'محتوا',
            similarity: `${Math.round((item.similarity || 0) * 100)}%`,
            date: item.datetime ? this.formatDate(item.datetime) : '',
            source: item.source || 'نامشخص',
          })),
        };
      case 'bar':
        return {
          series: [
            {
              name: 'شباهت',
              data: data.map((item) =>
                Math.round((item.similarity || 0) * 100)
              ),
            },
          ],
          categories: data.map((item, index) => `محتوا ${index + 1}`),
        };
      case 'table':
        return {
          data: data.map((item) => ({
            محتوا: item.content?.substring(0, 50) + '...' || 'محتوا',
            شباهت: `${Math.round((item.similarity || 0) * 100)}%`,
            تاریخ: item.datetime ? this.formatDate(item.datetime) : '',
            منبع: item.source || 'نامشخص',
          })),
        };
      default:
        return { data };
    }
  }
}

// Trends graph transformer (for multiple categories)
class TrendsGraphTransformer extends BaseTransformer {
  transform(data: any, chartType: ChartType): ChartData {
    if (typeof data === 'object' && !Array.isArray(data)) {
      // Multiple categories data
      const categories = Object.keys(data);
      const series: ChartSeries[] = categories.map((category) => ({
        name: category,
        data: data[category].map((item: any) => [
          new Date(item.datetime).getTime(),
          item.value,
        ]),
      }));

      const timeCategories =
        data[categories[0]]?.map((item: any) =>
          this.formatDate(item.datetime)
        ) || [];

      return { series, categories: timeCategories };
    }

    // Fallback to regular trends transformer
    return new TrendsTransformer().transform(data, chartType);
  }
}

// Transformer factory
export class DataTransformerFactory {
  private static transformers: Record<string, DataTransformer> = {
    process: new ProcessTransformer(),
    advance: new ProcessTransformer(),
    posts: new PostsTransformer(),
    cloud: new CloudTransformer(),
    source_info: new SourceInfoTransformer(),
    top_sources: new SourceInfoTransformer(),
    content_type_distribution: new ContentTypeDistributionTransformer(),
    search_in_source: new PostsTransformer(),
    similar_phrases: new SearchSuggestTransformer(),
    source_suggest: new SearchSuggestTransformer(),
    categories: new CategoriesTransformer(),
    sentiments: new SentimentsTransformer(),
    statistical: new StatisticalTransformer(),
    search_suggest: new SearchSuggestTransformer(),
    offensive: new OffensiveTransformer(),
    advertise: new AdvertiseTransformer(),
    influence_graph: new InfluenceGraphTransformer(),
    impact_graph: new ImpactGraphTransformer(),
    related_content: new RelatedContentTransformer(),
    emotion: new EmotionTransformer(),
    trends: new TrendsTransformer(),
    trends_graph: new TrendsGraphTransformer(),
    trends_statistical: new StatisticalTransformer(),
    trends_content_age: new CategoriesTransformer(),
    trends_account_credential: new CategoriesTransformer(),
    opinion_graph: new OpinionGraphTransformer(),
    opinion_emotion: new EmotionTransformer(),
    opinion_sentiments: new SentimentsTransformer(),
    opinion_age: new CategoriesTransformer(),
    opinion_gender: new CategoriesTransformer(),
    opinion_categories: new CategoriesTransformer(),
    opinion_top_sources: new SourceInfoTransformer(),
  };

  static getTransformer(reportType: string): DataTransformer | null {
    return this.transformers[reportType] || null;
  }

  static transform(
    data: any,
    widget: any,
    reportType: string,
    chartType: ChartType
  ): ChartData {
    const transformer = this.getTransformer(reportType);
    if (!transformer) {
      console.warn(`No transformer found for report type: ${reportType}`);
      return { data };
    }

    try {
      return transformer.transform(data, chartType, widget);
    } catch (error) {
      console.error(`Error transforming data for ${reportType}:`, error);
      return { data };
    }
  }
}
