import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import React, { useState, useEffect } from 'react';
import HorizontalEqualizer from './components/HorizontalEqualizer';
import CircularProgress from './components/CircularProgress';
import SparklineDots from './components/SparklineDots';
import BlockEqualizer from './components/BlockEqualizer';
import Input from './components/Input';
import { LockKeyholeOpen, UserSquare2 } from 'lucide-react';
import BottomEqualizer from './components/BottomEqualizer';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';

const LoadingScreen = () => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm">
      <div className="border-t-primary-500 h-24 w-24 animate-spin rounded-full border-2"></div>
    </div>
  );
};

export default function Login() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const [credit, setCredit] = useState('');
  const [password, setPassword] = useState('');
  const { login } = useAuth();

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!credit.trim()) {
      alert('لطفا نام کاربری را وارد کنید');
      return;
    }

    if (!password.trim()) {
      alert('لطفا کلمه عبور را وارد کنید');
      return;
    }

    setIsSubmitting(true);

    try {
      await login(credit.trim(), password);
      // Successful login - navigation will happen automatically
      navigate('/dashboard');
    } catch (err) {
      // Show error as browser alert
      const errorMessage =
        err instanceof Error ? err.message : 'خطا در ورود به سامانه';
      alert(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Simplified change handlers
  const handleCreditChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCredit(e.target.value);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
  };

  return (
    <>
      {isLoading && <LoadingScreen />}

      <div className="flex h-screen justify-center">
        <div
          className={`bg-dotted relative h-[900px] w-full max-w-[2000px] overflow-hidden transition-opacity duration-1000 ${
            isLoading ? 'opacity-0' : 'opacity-100'
          }`}
        >
          {/* Right Section - Hidden on mobile */}
          <div className="hidden lg:absolute lg:top-0 lg:right-0 lg:block lg:h-full lg:w-1/3 lg:overflow-hidden">
            <div className="flex h-full flex-col items-center">
              <div className="flex flex-row items-center justify-center px-4">
                <img
                  src="/images/globe.png"
                  alt="Globe"
                  width={1000}
                  height={1000}
                  className="h-[137px] w-[137px]"
                />

                <div className="h-[75px] w-[417px]">
                  <HorizontalEqualizer />
                </div>
              </div>
              <div className="flex flex-row justify-around px-4">
                <div className="flex flex-col">
                  <div className="p-4">
                    <CircularProgress targetPercentage={60} changeDelay={500} />
                  </div>
                  <div className="p-4">
                    <CircularProgress
                      targetPercentage={90}
                      changeDelay={1000}
                    />
                  </div>
                </div>
                <div className="flex flex-col justify-center gap-4">
                  <DotLottieReact
                    src="/lottie/vertical-equalizer.json"
                    loop
                    autoplay
                    className="h-[120px] w-[374px]"
                  />
                  <div className="flex flex-col gap-12">
                    {[31, 40, 40].map((columns, index) => (
                      <SparklineDots
                        key={index}
                        rows={3}
                        columns={columns}
                        shape={'circle'}
                        squareSize={7}
                        colors={[
                          { color: '#707070', chance: 1 },
                          { color: '#ffffff', chance: 1 },
                          { color: '#242424', chance: 2 },
                          { color: '#121212', chance: 3 },
                          { color: 'transparent', chance: 3 },
                          { color: '#0f0f0f', chance: 4 },
                        ]}
                      />
                    ))}
                  </div>
                </div>
              </div>
              <div className="flex h-[150px] w-full flex-col px-12">
                <BlockEqualizer />
              </div>
              <div className="flex flex-row items-center justify-around px-4">
                <DotLottieReact
                  src="/lottie/progress-bars-chart-rounded.json"
                  loop
                  autoplay
                  className="h-[190px] w-[190px]"
                />
                <DotLottieReact
                  src="/lottie/radar-complex.json"
                  loop
                  autoplay
                  className="h-[190px] w-[190px]"
                />
              </div>
            </div>
          </div>

          {/* Center Section - Login Form */}
          <div className="flex h-full flex-col items-center px-4 pt-10 lg:absolute lg:right-1/3 lg:w-1/3">
            <div className="flex flex-col items-center">
              <DotLottieReact
                src="/lottie/globe.json"
                loop
                autoplay
                className="h-[250px] w-[250px] md:h-[350px] md:w-[350px] lg:h-[450px] lg:w-[450px]"
              />
              <h1 className="text-primary-500 text-2xl font-black md:text-3xl lg:text-4xl">
                مرکز هشدار
              </h1>
              <form
                onSubmit={handleLogin}
                className="z-50 mt-4 flex w-full max-w-md flex-col gap-4 px-4"
              >
                <Input
                  value={credit}
                  onChange={handleCreditChange}
                  id={'username'}
                  placeholder={'نام کاربری'}
                  prependIcon={<UserSquare2 className="text-primary-500" />}
                  disabled={isSubmitting}
                />
                <Input
                  value={password}
                  onChange={handlePasswordChange}
                  id={'password'}
                  placeholder={'کلمه عبور'}
                  type="password"
                  prependIcon={<LockKeyholeOpen className="text-primary-500" />}
                  disabled={isSubmitting}
                />
                <button
                  type="submit"
                  className="bg-primary-500 flex w-full cursor-pointer items-center justify-center rounded-lg p-3 font-medium text-white disabled:cursor-not-allowed disabled:opacity-50"
                  disabled={isSubmitting || !credit || !password}
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                      در حال ورود...
                    </div>
                  ) : (
                    'ورود به سامانه'
                  )}
                </button>
              </form>
            </div>
          </div>

          {/* Left Section - Hidden on mobile */}
          <div className="hidden lg:absolute lg:top-0 lg:left-0 lg:block lg:h-full lg:w-1/3 lg:overflow-hidden">
            <div className="flex h-full flex-col items-center">
              <DotLottieReact
                src="/lottie/iran-map.json"
                loop
                autoplay
                className="h-[400px] w-[600px]"
              />
              <div className="mb-4 flex flex-row items-center justify-around">
                <div className="flex flex-col items-center justify-around">
                  {[30, 39, 35, 30].map((columns, index) => (
                    <SparklineDots
                      key={index}
                      rows={3}
                      columns={columns}
                      shape={'circle'}
                      squareSize={5}
                      colors={[
                        { color: '#707070', chance: 1 },
                        { color: '#ffffff', chance: 1 },
                        { color: '#242424', chance: 2 },
                        { color: '#121212', chance: 3 },
                        { color: 'transparent', chance: 3 },
                        { color: '#0f0f0f', chance: 4 },
                      ]}
                    />
                  ))}
                </div>
                <div className="max-w-[160px] p-4">
                  <CircularProgress targetPercentage={35} changeDelay={2000} />
                </div>
              </div>
              <SparklineDots
                rows={3}
                columns={31}
                shape={'square'}
                colors={[
                  { color: '#19ddd5', chance: 1 },
                  { color: '#ffffff', chance: 1 },
                  { color: '#242424', chance: 2 },
                  { color: '#121212', chance: 3 },
                  { color: 'transparent', chance: 3 },
                  { color: '#0f0f0f', chance: 4 },
                ]}
              />
              <div className="mt-2"></div>
              <div className="flex flex-row">
                <img
                  src="/images/icons/rotational-progress-15-op-50.svg"
                  alt="Sample circular progress 1"
                  width={100}
                  height={100}
                  className="h-[150px] w-[150px] animate-spin"
                />
                <DotLottieReact
                  src="/lottie/progress-bars-chart.json"
                  loop
                  autoplay
                  className="h-[150px] w-[150px]"
                />
                <DotLottieReact
                  src="/lottie/radar.json"
                  loop
                  autoplay
                  className="h-[145px] w-[145px]"
                />
              </div>
            </div>
          </div>

          {/* Bottom Equalizer */}
          <div className="fixed inset-x-0 bottom-0 z-50 h-16">
            <BottomEqualizer />
          </div>
        </div>
      </div>
    </>
  );
}
